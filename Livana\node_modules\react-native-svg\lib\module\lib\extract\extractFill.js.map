{"version": 3, "names": ["extractBrush", "extractOpacity", "processColor", "fillRules", "evenodd", "nonzero", "defaultFill", "type", "payload", "extractFill", "o", "props", "inherited", "fill", "fillRule", "fillOpacity", "push"], "sourceRoot": "../../../../src", "sources": ["lib/extract/extractFill.ts"], "mappings": "AAAA,OAAOA,YAAY,MAAM,gBAAgB;AACzC,OAAOC,cAAc,MAAM,kBAAkB;AAE7C,SAASC,YAAY,QAAQ,cAAc;AAE3C,MAAMC,SAA+C,GAAG;EACtDC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE;AACX,CAAC;AAED,MAAMC,WAAW,GAAG;EAAEC,IAAI,EAAE,CAAC;EAAEC,OAAO,EAAEN,YAAY,CAAC,OAAO;AAAE,CAAC;AAE/D,eAAe,SAASO,WAAWA,CACjCC,CAAiB,EACjBC,KAAgB,EAChBC,SAAmB,EACnB;EACA,MAAM;IAAEC,IAAI;IAAEC,QAAQ;IAAEC;EAAY,CAAC,GAAGJ,KAAK;EAC7C,IAAIE,IAAI,IAAI,IAAI,EAAE;IAChBD,SAAS,CAACI,IAAI,CAAC,MAAM,CAAC;IACtBN,CAAC,CAACG,IAAI,GACJ,CAACA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,GAAGP,WAAW,GAAGN,YAAY,CAACa,IAAI,CAAC;EACxE,CAAC,MAAM;IACL;IACAH,CAAC,CAACG,IAAI,GAAGP,WAAW;EACtB;EACA,IAAIS,WAAW,IAAI,IAAI,EAAE;IACvBH,SAAS,CAACI,IAAI,CAAC,aAAa,CAAC;IAC7BN,CAAC,CAACK,WAAW,GAAGd,cAAc,CAACc,WAAW,CAAC;EAC7C;EACA,IAAID,QAAQ,IAAI,IAAI,EAAE;IACpBF,SAAS,CAACI,IAAI,CAAC,UAAU,CAAC;IAC1BN,CAAC,CAACI,QAAQ,GAAGA,QAAQ,IAAIX,SAAS,CAACW,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;EAC5D;AACF", "ignoreList": []}