import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography, shadows } from '../../utils/theme';

export default function WeightWidget({ data, onUpdate, navigation }) {
  const currentWeight = data?.weight || null;
  const targetWeight = 70; // This would come from user profile
  
  return (
    <TouchableOpacity style={styles.container}>
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          <Ionicons name="scale" size={24} color={colors.primary} />
        </View>
        <Text style={styles.title}>Weight</Text>
        <Ionicons name="chevron-forward" size={16} color={colors.textLight} />
      </View>

      <View style={styles.content}>
        {currentWeight ? (
          <>
            <Text style={styles.value}>{currentWeight} kg</Text>
            <Text style={styles.target}>Target: {targetWeight} kg</Text>
          </>
        ) : (
          <Text style={styles.placeholder}>Tap to log weight</Text>
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.md,
    ...shadows.sm,
  },
  
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.primary + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  
  title: {
    flex: 1,
    fontSize: typography.md,
    fontWeight: typography.semibold,
    color: colors.text,
  },
  
  content: {
    alignItems: 'center',
    minHeight: 60,
    justifyContent: 'center',
  },
  
  value: {
    fontSize: typography.xxl,
    fontWeight: typography.bold,
    color: colors.primary,
  },
  
  target: {
    fontSize: typography.sm,
    color: colors.textSecondary,
  },
  
  placeholder: {
    fontSize: typography.md,
    color: colors.textSecondary,
  },
});
