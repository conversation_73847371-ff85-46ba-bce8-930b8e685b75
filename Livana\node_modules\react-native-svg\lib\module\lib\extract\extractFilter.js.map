{"version": 3, "names": ["React", "processColor", "extractBrush", "extractOpacity", "spaceReg", "extractFilter", "props", "x", "y", "width", "height", "result", "extracted", "extractIn", "in", "in1", "extractFeBlend", "in2", "mode", "extractFeColorMatrix", "values", "undefined", "Array", "isArray", "map", "num", "parseFloat", "split", "filter", "el", "isNaN", "console", "warn", "type", "extractFeComposite", "operator1", "operator", "for<PERSON>ach", "key", "Number", "defaultFill", "payload", "extractFeFlood", "floodColor", "floodOpacity", "extractFeGaussianBlur", "stdDeviation", "stdDeviationX", "stdDeviationY", "match", "edgeMode", "extractFeMerge", "parent", "nodes", "<PERSON><PERSON><PERSON><PERSON>", "children", "Children", "child", "cloneElement", "l", "length", "i", "push"], "sourceRoot": "../../../../src", "sources": ["lib/extract/extractFilter.ts"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAAqBC,YAAY,QAAQ,cAAc;AAavD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,cAAc,MAAM,kBAAkB;AAG7C,MAAMC,QAAQ,GAAG,KAAK;AAUtB,OAAO,MAAMC,aAAa,GACxBC,KAAiC,IACF;EAC/B,MAAM;IAAEC,CAAC;IAAEC,CAAC;IAAEC,KAAK;IAAEC,MAAM;IAAEC;EAAO,CAAC,GAAGL,KAAK;EAC7C,MAAMM,SAAqC,GAAG;IAC5CL,CAAC;IACDC,CAAC;IACDC,KAAK;IACLC,MAAM;IACNC;EACF,CAAC;EAED,OAAOC,SAAS;AAClB,CAAC;AAED,OAAO,MAAMC,SAAS,GAAIP,KAAsB,IAAK;EACnD,IAAIA,KAAK,CAACQ,EAAE,EAAE;IACZ,OAAO;MAAEC,GAAG,EAAET,KAAK,CAACQ;IAAG,CAAC;EAC1B;EACA,OAAO,CAAC,CAAC;AACX,CAAC;AAED,OAAO,MAAME,cAAc,GACzBV,KAA4B,IACL;EACvB,MAAMM,SAA6B,GAAG,CAAC,CAAC;EAExC,IAAIN,KAAK,CAACW,GAAG,EAAE;IACbL,SAAS,CAACK,GAAG,GAAGX,KAAK,CAACW,GAAG;EAC3B;EACA,IAAIX,KAAK,CAACY,IAAI,EAAE;IACdN,SAAS,CAACM,IAAI,GAAGZ,KAAK,CAACY,IAAI;EAC7B;EAEA,OAAON,SAAS;AAClB,CAAC;AAED,OAAO,MAAMO,oBAAoB,GAC/Bb,KAAkC,IACL;EAC7B,MAAMM,SAAmC,GAAG,CAAC,CAAC;EAE9C,IAAIN,KAAK,CAACc,MAAM,KAAKC,SAAS,EAAE;IAC9B,IAAIC,KAAK,CAACC,OAAO,CAACjB,KAAK,CAACc,MAAM,CAAC,EAAE;MAC/BR,SAAS,CAACQ,MAAM,GAAGd,KAAK,CAACc,MAAM,CAACI,GAAG,CAAEC,GAAG,IACtC,OAAOA,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAGC,UAAU,CAACD,GAAG,CAChD,CAAC;IACH,CAAC,MAAM,IAAI,OAAOnB,KAAK,CAACc,MAAM,KAAK,QAAQ,EAAE;MAC3CR,SAAS,CAACQ,MAAM,GAAG,CAACd,KAAK,CAACc,MAAM,CAAC;IACnC,CAAC,MAAM,IAAI,OAAOd,KAAK,CAACc,MAAM,KAAK,QAAQ,EAAE;MAC3CR,SAAS,CAACQ,MAAM,GAAGd,KAAK,CAACc,MAAM,CAC5BO,KAAK,CAACvB,QAAQ,CAAC,CACfoB,GAAG,CAACE,UAAU,CAAC,CACfE,MAAM,CAAEC,EAAU,IAAK,CAACC,KAAK,CAACD,EAAE,CAAC,CAAC;IACvC,CAAC,MAAM;MACLE,OAAO,CAACC,IAAI,CAAC,+CAA+C,CAAC;IAC/D;EACF;EACA,IAAI1B,KAAK,CAAC2B,IAAI,EAAE;IACdrB,SAAS,CAACqB,IAAI,GAAG3B,KAAK,CAAC2B,IAAI;EAC7B;EAEA,OAAOrB,SAAS;AAClB,CAAC;AAED,OAAO,MAAMsB,kBAAkB,GAC7B5B,KAAgC,IACL;EAC3B,MAAMM,SAAiC,GAAG;IACxCG,GAAG,EAAET,KAAK,CAACQ,EAAE,IAAI,EAAE;IACnBG,GAAG,EAAEX,KAAK,CAACW,GAAG,IAAI,EAAE;IACpBkB,SAAS,EAAE7B,KAAK,CAAC8B,QAAQ,IAAI;EAC/B,CAAC;EAEA,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAWC,OAAO,CAAEC,GAAG,IAAK;IACnD,IAAIhC,KAAK,CAACgC,GAAG,CAAC,KAAKjB,SAAS,EAAE;MAC5BT,SAAS,CAAC0B,GAAG,CAAC,GAAGC,MAAM,CAACjC,KAAK,CAACgC,GAAG,CAAC,CAAC,IAAI,CAAC;IAC1C;EACF,CAAC,CAAC;EAEF,OAAO1B,SAAS;AAClB,CAAC;AAED,MAAM4B,WAAW,GAAG;EAAEP,IAAI,EAAE,CAAC;EAAEQ,OAAO,EAAExC,YAAY,CAAC,OAAO;AAAgB,CAAC;AAC7E,eAAe,SAASyC,cAAcA,CACpCpC,KAA4B,EACR;EACpB,MAAMM,SAA6B,GAAG,CAAC,CAAC;EACxC,MAAM;IAAE+B,UAAU;IAAEC;EAAa,CAAC,GAAGtC,KAAK;EAE1C,IAAIqC,UAAU,IAAI,IAAI,EAAE;IACtB/B,SAAS,CAAC+B,UAAU,GAClB,CAACA,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,GACzCH,WAAW,GACVtC,YAAY,CAACyC,UAAU,CAAuB;EACvD,CAAC,MAAM;IACL;IACA/B,SAAS,CAAC+B,UAAU,GAAGH,WAAW;EACpC;EACA,IAAII,YAAY,IAAI,IAAI,EAAE;IACxBhC,SAAS,CAACgC,YAAY,GAAGzC,cAAc,CAACyC,YAAY,CAAC;EACvD;EACA,OAAOhC,SAAS;AAClB;AAEA,OAAO,MAAMiC,qBAAqB,GAChCvC,KAAmC,IACL;EAC9B,MAAMM,SAAoC,GAAG,CAAC,CAAC;EAE/C,IAAIU,KAAK,CAACC,OAAO,CAACjB,KAAK,CAACwC,YAAY,CAAC,EAAE;IACrClC,SAAS,CAACmC,aAAa,GAAGR,MAAM,CAACjC,KAAK,CAACwC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC5DlC,SAAS,CAACoC,aAAa,GAAGT,MAAM,CAACjC,KAAK,CAACwC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;EAC9D,CAAC,MAAM,IACL,OAAOxC,KAAK,CAACwC,YAAY,KAAK,QAAQ,IACtCxC,KAAK,CAACwC,YAAY,CAACG,KAAK,CAAC7C,QAAQ,CAAC,EAClC;IACA,MAAM0C,YAAY,GAAGxC,KAAK,CAACwC,YAAY,CAACnB,KAAK,CAACvB,QAAQ,CAAC;IACvDQ,SAAS,CAACmC,aAAa,GAAGR,MAAM,CAACO,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACtDlC,SAAS,CAACoC,aAAa,GAAGT,MAAM,CAACO,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;EACxD,CAAC,MAAM,IACL,OAAOxC,KAAK,CAACwC,YAAY,KAAK,QAAQ,IACrC,OAAOxC,KAAK,CAACwC,YAAY,KAAK,QAAQ,IACrC,CAACxC,KAAK,CAACwC,YAAY,CAACG,KAAK,CAAC7C,QAAQ,CAAE,EACtC;IACAQ,SAAS,CAACmC,aAAa,GAAGR,MAAM,CAACjC,KAAK,CAACwC,YAAY,CAAC,IAAI,CAAC;IACzDlC,SAAS,CAACoC,aAAa,GAAGT,MAAM,CAACjC,KAAK,CAACwC,YAAY,CAAC,IAAI,CAAC;EAC3D;EACA,IAAIxC,KAAK,CAAC4C,QAAQ,EAAE;IAClBtC,SAAS,CAACsC,QAAQ,GAAG5C,KAAK,CAAC4C,QAAQ;EACrC;EACA,OAAOtC,SAAS;AAClB,CAAC;AAED,OAAO,MAAMuC,cAAc,GAAGA,CAC5B7C,KAA4B,EAC5B8C,MAAe,KACQ;EACvB,MAAMC,KAAoB,GAAG,EAAE;EAC/B,MAAMC,UAAU,GAAGhD,KAAK,CAACiD,QAAQ,GAC7BvD,KAAK,CAACwD,QAAQ,CAAChC,GAAG,CAAClB,KAAK,CAACiD,QAAQ,EAAGE,KAAK,iBACvCzD,KAAK,CAAC0D,YAAY,CAACD,KAAK,EAAE;IAAEL;EAAO,CAAC,CACtC,CAAC,GACD,EAAE;EACN,MAAMO,CAAC,GAAGL,UAAU,CAACM,MAAM;EAC3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAEE,CAAC,EAAE,EAAE;IAC1B,MAAM;MACJvD,KAAK,EAAE;QAAEQ,EAAE,EAAEC;MAAI;IACnB,CAAC,GAAGuC,UAAU,CAACO,CAAC,CAAC;IACjBR,KAAK,CAACS,IAAI,CAAC/C,GAAG,IAAI,EAAE,CAAC;EACvB;EAEA,OAAO;IAAEsC;EAAM,CAAC;AAClB,CAAC", "ignoreList": []}