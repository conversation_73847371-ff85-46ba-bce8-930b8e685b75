{"version": 3, "names": ["React", "extract", "<PERSON><PERSON><PERSON>", "RNSVGLine", "Line", "displayName", "defaultProps", "x1", "y1", "x2", "y2", "render", "props", "lineProps", "createElement", "_extends", "ref", "refMethod"], "sourceRoot": "../../../src", "sources": ["elements/Line.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,6BAA6B;AAErD,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,SAAS,MAAM,+BAA+B;AAWrD,eAAe,MAAMC,IAAI,SAASF,KAAK,CAAY;EACjD,OAAOG,WAAW,GAAG,MAAM;EAE3B,OAAOC,YAAY,GAAG;IACpBC,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE;EACN,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEC;IAAM,CAAC,GAAG,IAAI;IACtB,MAAM;MAAEL,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC;IAAG,CAAC,GAAGE,KAAK;IAChC,MAAMC,SAAS,GAAG;MAChB,GAAGZ,OAAO,CAAC,IAAI,EAAEW,KAAK,CAAC;MACvBL,EAAE;MACFC,EAAE;MACFC,EAAE;MACFC;IACF,CAAC;IACD,oBACEV,KAAA,CAAAc,aAAA,CAACX,SAAS,EAAAY,QAAA;MACRC,GAAG,EAAGA,GAAG,IAAK,IAAI,CAACC,SAAS,CAACD,GAAoC;IAAE,GAC/DH,SAAS,CACd,CAAC;EAEN;AACF", "ignoreList": []}