import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { spacing, typography, shadows } from '../../utils/theme';
import { useTheme } from '../../context/ThemeContext';

export default function StepsWidget({ data, onUpdate }) {
  const { currentTheme: colors } = useTheme();
  const steps = data?.steps || 0;
  const target = 10000;
  const progress = Math.min(steps / target, 1);
  const percentage = Math.round(progress * 100);

  const handlePress = () => {
    // Navigate to detailed steps view or allow manual entry
    console.log('Steps widget pressed');
  };

  return (
    <TouchableOpacity
      style={[styles.container, { backgroundColor: colors.surface, ...shadows.card }]}
      onPress={handlePress}
      activeOpacity={0.8}
    >
      <View style={styles.header}>
        <View style={[styles.iconContainer, { backgroundColor: colors.steps + '20' }]}>
          <Ionicons name="walk" size={24} color={colors.steps} />
        </View>
        <Text style={[styles.title, { color: colors.text }]}>Steps</Text>
        <Ionicons name="chevron-forward" size={16} color={colors.textLight} />
      </View>

      <View style={styles.content}>
        <Text style={[styles.value, { color: colors.steps }]}>{steps.toLocaleString()}</Text>
        <Text style={[styles.target, { color: colors.textSecondary }]}>of {target.toLocaleString()}</Text>

        <View style={styles.progressContainer}>
          <View style={[styles.progressBar, { backgroundColor: colors.surfaceLight }]}>
            <View style={[styles.progressFill, {
              width: `${percentage}%`,
              backgroundColor: colors.steps
            }]} />
          </View>
          <Text style={[styles.percentage, { color: colors.steps }]}>{percentage}%</Text>
        </View>
      </View>

      <View style={styles.footer}>
        <View style={styles.statusContainer}>
          <Ionicons
            name={steps >= target ? "checkmark-circle" : "time"}
            size={16}
            color={steps >= target ? colors.success : colors.textSecondary}
          />
          <Text style={[
            styles.status,
            { color: colors.textSecondary },
            steps >= target && { color: colors.success, ...styles.statusComplete }
          ]}>
            {steps >= target ? 'Goal reached!' : `${target - steps} to go`}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.md,
  },

  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },

  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },

  title: {
    flex: 1,
    fontSize: typography.md,
    fontWeight: typography.semibold,
  },
  
  content: {
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  
  value: {
    fontSize: typography.xxxl,
    fontWeight: typography.bold,
  },

  target: {
    fontSize: typography.sm,
    marginBottom: spacing.md,
  },
  
  progressContainer: {
    width: '100%',
    alignItems: 'center',
  },
  
  progressBar: {
    width: '100%',
    height: 8,
    borderRadius: 4,
    marginBottom: spacing.xs,
  },

  progressFill: {
    height: '100%',
    borderRadius: 4,
  },

  percentage: {
    fontSize: typography.sm,
    fontWeight: typography.medium,
  },
  
  footer: {
    alignItems: 'center',
  },
  
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  status: {
    fontSize: typography.sm,
    marginLeft: spacing.xs,
  },

  statusComplete: {
    fontWeight: typography.medium,
  },
});
