import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography, shadows } from '../../utils/theme';

export default function StepsWidget({ data, onUpdate }) {
  const steps = data?.steps || 0;
  const target = 10000;
  const progress = Math.min(steps / target, 1);
  const percentage = Math.round(progress * 100);

  const handlePress = () => {
    // Navigate to detailed steps view or allow manual entry
    console.log('Steps widget pressed');
  };

  return (
    <TouchableOpacity style={styles.container} onPress={handlePress}>
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          <Ionicons name="walk" size={24} color={colors.steps} />
        </View>
        <Text style={styles.title}>Steps</Text>
        <Ionicons name="chevron-forward" size={16} color={colors.textLight} />
      </View>

      <View style={styles.content}>
        <Text style={styles.value}>{steps.toLocaleString()}</Text>
        <Text style={styles.target}>of {target.toLocaleString()}</Text>
        
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, { width: `${percentage}%` }]} />
          </View>
          <Text style={styles.percentage}>{percentage}%</Text>
        </View>
      </View>

      <View style={styles.footer}>
        <View style={styles.statusContainer}>
          <Ionicons 
            name={steps >= target ? "checkmark-circle" : "time"} 
            size={16} 
            color={steps >= target ? colors.success : colors.textSecondary} 
          />
          <Text style={[styles.status, steps >= target && styles.statusComplete]}>
            {steps >= target ? 'Goal reached!' : `${target - steps} to go`}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.md,
    ...shadows.sm,
  },
  
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.steps + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  
  title: {
    flex: 1,
    fontSize: typography.md,
    fontWeight: typography.semibold,
    color: colors.text,
  },
  
  content: {
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  
  value: {
    fontSize: typography.xxxl,
    fontWeight: typography.bold,
    color: colors.steps,
  },
  
  target: {
    fontSize: typography.sm,
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  
  progressContainer: {
    width: '100%',
    alignItems: 'center',
  },
  
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: colors.surfaceLight,
    borderRadius: 4,
    marginBottom: spacing.xs,
  },
  
  progressFill: {
    height: '100%',
    backgroundColor: colors.steps,
    borderRadius: 4,
  },
  
  percentage: {
    fontSize: typography.sm,
    fontWeight: typography.medium,
    color: colors.steps,
  },
  
  footer: {
    alignItems: 'center',
  },
  
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  status: {
    fontSize: typography.sm,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
  },
  
  statusComplete: {
    color: colors.success,
    fontWeight: typography.medium,
  },
});
