import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  TextInput,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { spacing, typography } from '../../utils/theme';
import { useTheme } from '../../context/ThemeContext';

export default function PersonalizationScreen({ navigation, route }) {
  const { currentTheme: colors } = useTheme();
  const { goalData } = route.params || {};
  const [formData, setFormData] = useState({
    name: '',
    age: '',
    weight: '',
    height: '',
    activityLevel: '',
    preferredWorkoutTime: '',
    dietaryPreferences: [],
  });

  const activityLevels = [
    { value: 'sedentary', label: 'Sedentary', description: 'Little to no exercise' },
    { value: 'light', label: 'Lightly Active', description: 'Light exercise 1-3 days/week' },
    { value: 'moderate', label: 'Moderately Active', description: 'Moderate exercise 3-5 days/week' },
    { value: 'very', label: 'Very Active', description: 'Hard exercise 6-7 days/week' },
  ];

  const workoutTimes = [
    { value: 'early_morning', label: 'Early Morning', time: '5:00-7:00 AM' },
    { value: 'morning', label: 'Morning', time: '7:00-10:00 AM' },
    { value: 'midday', label: 'Midday', time: '10:00 AM-2:00 PM' },
    { value: 'afternoon', label: 'Afternoon', time: '2:00-6:00 PM' },
    { value: 'evening', label: 'Evening', time: '6:00-9:00 PM' },
  ];

  const dietaryOptions = [
    { value: 'vegetarian', label: 'Vegetarian' },
    { value: 'vegan', label: 'Vegan' },
    { value: 'gluten_free', label: 'Gluten-Free' },
    { value: 'dairy_free', label: 'Dairy-Free' },
    { value: 'low_carb', label: 'Low Carb' },
    { value: 'mediterranean', label: 'Mediterranean' },
    { value: 'none', label: 'No Restrictions' },
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleDietaryToggle = (value) => {
    if (value === 'none') {
      setFormData(prev => ({
        ...prev,
        dietaryPreferences: ['none'],
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        dietaryPreferences: prev.dietaryPreferences.includes('none')
          ? [value]
          : prev.dietaryPreferences.includes(value)
          ? prev.dietaryPreferences.filter(pref => pref !== value)
          : [...prev.dietaryPreferences.filter(pref => pref !== 'none'), value],
      }));
    }
  };

  const handleComplete = () => {
    if (!formData.name.trim()) {
      Alert.alert('Required Field', 'Please enter your name to continue.');
      return;
    }

    const completeProfile = {
      ...formData,
      ...goalData,
      completedAt: new Date().toISOString(),
    };

    navigation.navigate('OnboardingComplete', { userProfile: completeProfile });
  };

  const canComplete = formData.name.trim().length > 0;

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>Tell Us About Yourself</Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            Help us personalize your Livana experience
          </Text>
        </View>

        {/* Basic Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Basic Information</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Name *</Text>
            <TextInput
              style={[styles.textInput, {
                borderColor: colors.surfaceLight,
                color: colors.text,
                backgroundColor: colors.surface
              }]}
              placeholder="Enter your name"
              placeholderTextColor={colors.textLight}
              value={formData.name}
              onChangeText={(value) => handleInputChange('name', value)}
            />
          </View>

          <View style={styles.row}>
            <View style={[styles.inputGroup, { flex: 1, marginRight: spacing.sm }]}>
              <Text style={styles.inputLabel}>Age</Text>
              <TextInput
                style={styles.textInput}
                placeholder="25"
                placeholderTextColor={colors.textLight}
                value={formData.age}
                onChangeText={(value) => handleInputChange('age', value)}
                keyboardType="numeric"
              />
            </View>

            <View style={[styles.inputGroup, { flex: 1, marginLeft: spacing.sm }]}>
              <Text style={styles.inputLabel}>Weight (kg)</Text>
              <TextInput
                style={styles.textInput}
                placeholder="70"
                placeholderTextColor={colors.textLight}
                value={formData.weight}
                onChangeText={(value) => handleInputChange('weight', value)}
                keyboardType="numeric"
              />
            </View>
          </View>
        </View>

        {/* Activity Level */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Activity Level</Text>
          {activityLevels.map((level) => (
            <TouchableOpacity
              key={level.value}
              style={[
                styles.optionButton,
                formData.activityLevel === level.value && styles.optionButtonSelected
              ]}
              onPress={() => handleInputChange('activityLevel', level.value)}
            >
              <View style={styles.optionContent}>
                <View>
                  <Text style={[
                    styles.optionTitle,
                    formData.activityLevel === level.value && styles.optionTitleSelected
                  ]}>
                    {level.label}
                  </Text>
                  <Text style={styles.optionDescription}>{level.description}</Text>
                </View>
                <View style={[
                  styles.radioButton,
                  formData.activityLevel === level.value && styles.radioButtonSelected
                ]}>
                  {formData.activityLevel === level.value && (
                    <View style={styles.radioButtonInner} />
                  )}
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Preferred Workout Time */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Preferred Workout Time</Text>
          {workoutTimes.map((time) => (
            <TouchableOpacity
              key={time.value}
              style={[
                styles.optionButton,
                formData.preferredWorkoutTime === time.value && styles.optionButtonSelected
              ]}
              onPress={() => handleInputChange('preferredWorkoutTime', time.value)}
            >
              <View style={styles.optionContent}>
                <View>
                  <Text style={[
                    styles.optionTitle,
                    formData.preferredWorkoutTime === time.value && styles.optionTitleSelected
                  ]}>
                    {time.label}
                  </Text>
                  <Text style={styles.optionDescription}>{time.time}</Text>
                </View>
                <View style={[
                  styles.radioButton,
                  formData.preferredWorkoutTime === time.value && styles.radioButtonSelected
                ]}>
                  {formData.preferredWorkoutTime === time.value && (
                    <View style={styles.radioButtonInner} />
                  )}
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Dietary Preferences */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Dietary Preferences</Text>
          <Text style={styles.sectionSubtitle}>Select all that apply</Text>
          
          <View style={styles.checkboxGrid}>
            {dietaryOptions.map((option) => {
              const isSelected = formData.dietaryPreferences.includes(option.value);
              return (
                <TouchableOpacity
                  key={option.value}
                  style={[styles.checkboxOption, isSelected && styles.checkboxOptionSelected]}
                  onPress={() => handleDietaryToggle(option.value)}
                >
                  <Text style={[
                    styles.checkboxText,
                    isSelected && styles.checkboxTextSelected
                  ]}>
                    {option.label}
                  </Text>
                  {isSelected && (
                    <Ionicons name="checkmark" size={16} color={colors.primary} />
                  )}
                </TouchableOpacity>
              );
            })}
          </View>
        </View>
      </ScrollView>

      {/* Complete Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.completeButton, !canComplete && styles.completeButtonDisabled]}
          onPress={handleComplete}
          disabled={!canComplete}
        >
          <Text style={[styles.completeButtonText, !canComplete && styles.completeButtonTextDisabled]}>
            Complete Setup
          </Text>
          <Ionicons 
            name="checkmark-circle" 
            size={20} 
            color={canComplete ? colors.surface : colors.textLight} 
          />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  
  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  
  header: {
    paddingVertical: spacing.lg,
    alignItems: 'center',
  },
  
  title: {
    fontSize: typography.xxl,
    fontWeight: typography.bold,
    color: colors.text,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  
  subtitle: {
    fontSize: typography.md,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  
  section: {
    marginBottom: spacing.xl,
  },
  
  sectionTitle: {
    fontSize: typography.lg,
    fontWeight: typography.semibold,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  
  sectionSubtitle: {
    fontSize: typography.sm,
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  
  inputGroup: {
    marginBottom: spacing.md,
  },
  
  inputLabel: {
    fontSize: typography.sm,
    fontWeight: typography.medium,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  
  textInput: {
    borderWidth: 1,
    borderColor: colors.surfaceLight,
    borderRadius: 8,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    fontSize: typography.md,
    color: colors.text,
    backgroundColor: colors.surface,
  },
  
  row: {
    flexDirection: 'row',
  },
  
  optionButton: {
    backgroundColor: colors.surface,
    borderRadius: 8,
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.surfaceLight,
  },
  
  optionButtonSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.primaryLight + '10',
  },
  
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  
  optionTitle: {
    fontSize: typography.md,
    fontWeight: typography.medium,
    color: colors.text,
  },
  
  optionTitleSelected: {
    color: colors.primary,
  },
  
  optionDescription: {
    fontSize: typography.sm,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.textLight,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  radioButtonSelected: {
    borderColor: colors.primary,
  },
  
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: colors.primary,
  },
  
  checkboxGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -spacing.xs,
  },
  
  checkboxOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    borderRadius: 20,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    margin: spacing.xs,
    borderWidth: 1,
    borderColor: colors.surfaceLight,
  },
  
  checkboxOptionSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.primaryLight + '10',
  },
  
  checkboxText: {
    fontSize: typography.sm,
    color: colors.text,
    marginRight: spacing.xs,
  },
  
  checkboxTextSelected: {
    color: colors.primary,
    fontWeight: typography.medium,
  },
  
  footer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    borderTopWidth: 1,
    borderTopColor: colors.surfaceLight,
    alignItems: 'center',
  },
  
  completeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.xl,
    borderRadius: 25,
  },
  
  completeButtonDisabled: {
    backgroundColor: colors.surfaceLight,
  },
  
  completeButtonText: {
    fontSize: typography.md,
    fontWeight: typography.medium,
    color: colors.surface,
    marginRight: spacing.sm,
  },
  
  completeButtonTextDisabled: {
    color: colors.textLight,
  },
});
