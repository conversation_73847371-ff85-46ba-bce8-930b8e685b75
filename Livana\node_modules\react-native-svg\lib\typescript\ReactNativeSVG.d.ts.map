{"version": 3, "file": "ReactNativeSVG.d.ts", "sourceRoot": "", "sources": ["../../src/ReactNativeSVG.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,kBAAkB,CAAC;AACrC,OAAO,EACL,QAAQ,EACR,SAAS,EACT,MAAM,EACN,UAAU,EACV,KAAK,EACL,MAAM,EACN,MAAM,EACN,UAAU,EACV,UAAU,EACV,MAAM,EACN,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,QAAQ,EACT,MAAM,OAAO,CAAC;AAEf,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAE9C,OAAO,EACL,WAAW,EACX,aAAa,EACb,SAAS,EACT,YAAY,EACZ,kBAAkB,EAClB,gBAAgB,EAChB,mBAAmB,EACnB,YAAY,EACZ,aAAa,EACb,WAAW,EACX,kBAAkB,EAClB,UAAU,EACV,UAAU,EACV,SAAS,EACT,mBAAmB,EACnB,WAAW,EACX,SAAS,EACT,SAAS,EACT,YAAY,EACZ,mBAAmB,EACnB,SAAS,EACT,eAAe,EACf,WAAW,EACX,WAAW,EACX,SAAS,EACT,aAAa,EACb,UAAU,EACV,QAAQ,EACT,MAAM,UAAU,CAAC;AAElB,OAAO,EACL,YAAY,EACZ,oBAAoB,EACpB,QAAQ,EACR,MAAM,EACN,SAAS,EACT,UAAU,EACV,aAAa,EACb,YAAY,GACb,MAAM,cAAc,CAAC;AAEtB,YAAY,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AACrD,YAAY,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AACzD,YAAY,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AACvD,YAAY,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAC/D,YAAY,EAAE,kBAAkB,EAAE,MAAM,kCAAkC,CAAC;AAC3E,YAAY,EAAE,wBAAwB,EAAE,MAAM,wCAAwC,CAAC;AACvF,YAAY,EACV,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,GACb,MAAM,gDAAgD,CAAC;AACxD,YAAY,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAC;AACvE,YAAY,EAAE,qBAAqB,EAAE,MAAM,qCAAqC,CAAC;AACjF,YAAY,EAAE,sBAAsB,EAAE,MAAM,sCAAsC,CAAC;AACnF,YAAY,EAAE,sBAAsB,EAAE,MAAM,sCAAsC,CAAC;AACnF,YAAY,EAAE,mBAAmB,EAAE,MAAM,mCAAmC,CAAC;AAC7E,YAAY,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AACzE,YAAY,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAC/D,YAAY,EAAE,mBAAmB,EAAE,MAAM,mCAAmC,CAAC;AAC7E,YAAY,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAC/D,YAAY,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAC/D,YAAY,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAC;AACvE,YAAY,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AACzE,YAAY,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AACjE,YAAY,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AACzE,YAAY,EAAE,uBAAuB,EAAE,MAAM,uCAAuC,CAAC;AACrF,YAAY,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAC;AACvE,YAAY,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAC7D,YAAY,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AACzE,YAAY,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAC7D,YAAY,EAAE,0BAA0B,EAAE,MAAM,oCAAoC,CAAC;AACrF,YAAY,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAC;AACnE,YAAY,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AAC3C,YAAY,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AACnD,YAAY,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AACjD,YAAY,EAAE,mBAAmB,EAAE,MAAM,2BAA2B,CAAC;AACrE,YAAY,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AACrD,YAAY,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AACjD,YAAY,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AACjD,YAAY,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AACvD,YAAY,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AACvD,YAAY,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AACzD,YAAY,EAAE,mBAAmB,EAAE,MAAM,2BAA2B,CAAC;AACrE,YAAY,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AACjD,YAAY,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AACjD,YAAY,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAC/C,YAAY,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AACrD,YAAY,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AACjD,YAAY,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AACzD,YAAY,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AACnD,YAAY,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAE/C,cAAc,qBAAqB,CAAC;AAEpC,OAAO,EACL,SAAS,EACT,SAAS,EACT,KAAK,EACL,WAAW,EACX,aAAa,EACb,SAAS,EACT,YAAY,EACZ,kBAAkB,EAClB,gBAAgB,EAChB,mBAAmB,EACnB,YAAY,EACZ,aAAa,EACb,WAAW,EACX,kBAAkB,EAClB,UAAU,EACV,UAAU,EACV,SAAS,EACT,mBAAmB,EACnB,WAAW,EACX,SAAS,EACT,SAAS,EACT,YAAY,EACZ,mBAAmB,EACnB,SAAS,EACT,eAAe,EACf,WAAW,EACX,WAAW,EACX,SAAS,EACT,aAAa,EACb,UAAU,EACV,QAAQ,EACR,KAAK,EACL,MAAM,EACN,UAAU,EACV,UAAU,EACV,MAAM,EACN,MAAM,GACP,CAAC;AAEF,YAAY,EACV,QAAQ,EACR,MAAM,EACN,UAAU,EACV,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,QAAQ,GACT,CAAC;AAEF,cAAc,YAAY,CAAC;AAC3B,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC"}