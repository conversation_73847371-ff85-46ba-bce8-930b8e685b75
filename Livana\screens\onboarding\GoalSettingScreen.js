import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography, commonStyles } from '../../utils/theme';

const goalCategories = [
  {
    id: 'fitness',
    title: 'Fitness Goals',
    icon: 'fitness',
    color: colors.steps,
    goals: [
      { id: 'daily_steps', title: 'Walk 10,000 steps daily', target: 10000, unit: 'steps' },
      { id: 'weekly_workouts', title: 'Exercise 3 times per week', target: 3, unit: 'workouts' },
      { id: 'strength_training', title: 'Strength training 2x/week', target: 2, unit: 'sessions' },
      { id: 'cardio_minutes', title: '150 minutes cardio/week', target: 150, unit: 'minutes' },
    ],
  },
  {
    id: 'nutrition',
    title: 'Nutrition Goals',
    icon: 'restaurant',
    color: colors.secondary,
    goals: [
      { id: 'water_intake', title: 'Drink 8 glasses of water daily', target: 8, unit: 'glasses' },
      { id: 'fruits_veggies', title: 'Eat 5 servings fruits/veggies', target: 5, unit: 'servings' },
      { id: 'home_cooked', title: 'Cook at home 5 days/week', target: 5, unit: 'days' },
      { id: 'mindful_eating', title: 'Practice mindful eating', target: 1, unit: 'daily' },
    ],
  },
  {
    id: 'mindfulness',
    title: 'Mindfulness Goals',
    icon: 'leaf',
    color: colors.mood,
    goals: [
      { id: 'daily_meditation', title: 'Meditate 10 minutes daily', target: 10, unit: 'minutes' },
      { id: 'gratitude_journal', title: 'Write 3 gratitudes daily', target: 3, unit: 'items' },
      { id: 'breathing_exercises', title: 'Practice breathing exercises', target: 1, unit: 'daily' },
      { id: 'digital_detox', title: '1 hour phone-free before bed', target: 1, unit: 'hour' },
    ],
  },
  {
    id: 'sleep',
    title: 'Sleep Goals',
    icon: 'moon',
    color: colors.sleep,
    goals: [
      { id: 'sleep_hours', title: 'Sleep 7-8 hours nightly', target: 8, unit: 'hours' },
      { id: 'bedtime_routine', title: 'Consistent bedtime routine', target: 1, unit: 'daily' },
      { id: 'screen_cutoff', title: 'No screens 1hr before bed', target: 1, unit: 'hour' },
      { id: 'wake_time', title: 'Consistent wake time', target: 1, unit: 'daily' },
    ],
  },
];

export default function GoalSettingScreen({ navigation, route }) {
  const { quizAnswers } = route.params || {};
  const [selectedGoals, setSelectedGoals] = useState([]);
  const [visionStatement, setVisionStatement] = useState('');

  const handleGoalToggle = (categoryId, goalId) => {
    const goalKey = `${categoryId}_${goalId}`;
    setSelectedGoals(prev => 
      prev.includes(goalKey)
        ? prev.filter(g => g !== goalKey)
        : [...prev, goalKey]
    );
  };

  const handleContinue = () => {
    const goalData = {
      selectedGoals,
      visionStatement,
      quizAnswers,
    };
    navigation.navigate('Personalization', { goalData });
  };

  const canContinue = selectedGoals.length > 0;

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Set Your Wellness Goals</Text>
          <Text style={styles.subtitle}>
            Choose the goals that resonate with your wellness journey. You can always adjust these later.
          </Text>
        </View>

        {/* Goal Categories */}
        {goalCategories.map((category) => (
          <View key={category.id} style={styles.categoryContainer}>
            <View style={styles.categoryHeader}>
              <View style={[styles.categoryIcon, { backgroundColor: category.color + '20' }]}>
                <Ionicons name={category.icon} size={24} color={category.color} />
              </View>
              <Text style={styles.categoryTitle}>{category.title}</Text>
            </View>

            <View style={styles.goalsContainer}>
              {category.goals.map((goal) => {
                const goalKey = `${category.id}_${goal.id}`;
                const isSelected = selectedGoals.includes(goalKey);
                
                return (
                  <TouchableOpacity
                    key={goal.id}
                    style={[styles.goalItem, isSelected && styles.goalItemSelected]}
                    onPress={() => handleGoalToggle(category.id, goal.id)}
                  >
                    <View style={styles.goalContent}>
                      <Text style={[styles.goalTitle, isSelected && styles.goalTitleSelected]}>
                        {goal.title}
                      </Text>
                      <Text style={styles.goalTarget}>
                        Target: {goal.target} {goal.unit}
                      </Text>
                    </View>
                    <View style={[styles.checkbox, isSelected && styles.checkboxSelected]}>
                      {isSelected && (
                        <Ionicons name="checkmark" size={16} color={colors.surface} />
                      )}
                    </View>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>
        ))}

        {/* Vision Statement */}
        <View style={styles.visionContainer}>
          <Text style={styles.visionTitle}>Your Wellness Vision</Text>
          <Text style={styles.visionSubtitle}>
            Write a short statement about what wellness means to you (optional)
          </Text>
          <TextInput
            style={styles.visionInput}
            placeholder="e.g., I want to feel energized, balanced, and present in my daily life..."
            placeholderTextColor={colors.textLight}
            value={visionStatement}
            onChangeText={setVisionStatement}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>
      </ScrollView>

      {/* Continue Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.continueButton, !canContinue && styles.continueButtonDisabled]}
          onPress={handleContinue}
          disabled={!canContinue}
        >
          <Text style={[styles.continueButtonText, !canContinue && styles.continueButtonTextDisabled]}>
            Continue to Personalization
          </Text>
          <Ionicons 
            name="arrow-forward" 
            size={20} 
            color={canContinue ? colors.surface : colors.textLight} 
          />
        </TouchableOpacity>
        
        {selectedGoals.length > 0 && (
          <Text style={styles.selectedCount}>
            {selectedGoals.length} goal{selectedGoals.length !== 1 ? 's' : ''} selected
          </Text>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  
  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  
  header: {
    paddingVertical: spacing.lg,
    alignItems: 'center',
  },
  
  title: {
    fontSize: typography.xxl,
    fontWeight: typography.bold,
    color: colors.text,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  
  subtitle: {
    fontSize: typography.md,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  
  categoryContainer: {
    marginBottom: spacing.lg,
  },
  
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  
  categoryTitle: {
    fontSize: typography.lg,
    fontWeight: typography.semibold,
    color: colors.text,
  },
  
  goalsContainer: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: spacing.sm,
  },
  
  goalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: spacing.md,
    borderRadius: 8,
    marginBottom: spacing.xs,
  },
  
  goalItemSelected: {
    backgroundColor: colors.primaryLight + '20',
  },
  
  goalContent: {
    flex: 1,
  },
  
  goalTitle: {
    fontSize: typography.md,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  
  goalTitleSelected: {
    color: colors.primary,
    fontWeight: typography.medium,
  },
  
  goalTarget: {
    fontSize: typography.sm,
    color: colors.textSecondary,
  },
  
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: colors.textLight,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  checkboxSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  
  visionContainer: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: spacing.lg,
    marginBottom: spacing.xl,
  },
  
  visionTitle: {
    fontSize: typography.lg,
    fontWeight: typography.semibold,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  
  visionSubtitle: {
    fontSize: typography.sm,
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  
  visionInput: {
    borderWidth: 1,
    borderColor: colors.surfaceLight,
    borderRadius: 8,
    padding: spacing.md,
    fontSize: typography.md,
    color: colors.text,
    minHeight: 80,
  },
  
  footer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    borderTopWidth: 1,
    borderTopColor: colors.surfaceLight,
    alignItems: 'center',
  },
  
  continueButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.xl,
    borderRadius: 25,
    marginBottom: spacing.sm,
  },
  
  continueButtonDisabled: {
    backgroundColor: colors.surfaceLight,
  },
  
  continueButtonText: {
    fontSize: typography.md,
    fontWeight: typography.medium,
    color: colors.surface,
    marginRight: spacing.sm,
  },
  
  continueButtonTextDisabled: {
    color: colors.textLight,
  },
  
  selectedCount: {
    fontSize: typography.sm,
    color: colors.textSecondary,
  },
});
