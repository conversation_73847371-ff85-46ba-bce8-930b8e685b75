import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography, shadows } from '../../utils/theme';

export default function FitnessWidget({ data, onUpdate, navigation }) {
  const workouts = data?.workouts || [];
  const weeklyTarget = 3;
  const thisWeekWorkouts = workouts.length; // Simplified for demo
  
  return (
    <TouchableOpacity style={styles.container}>
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          <Ionicons name="barbell" size={24} color={colors.steps} />
        </View>
        <Text style={styles.title}>Fitness</Text>
        <Ionicons name="chevron-forward" size={16} color={colors.textLight} />
      </View>

      <View style={styles.content}>
        <Text style={styles.value}>{thisWeekWorkouts}</Text>
        <Text style={styles.target}>of {weeklyTarget} workouts this week</Text>
        
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View style={[
              styles.progressFill,
              { width: `${Math.min((thisWeekWorkouts / weeklyTarget) * 100, 100)}%` }
            ]} />
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.md,
    ...shadows.sm,
  },
  
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.steps + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  
  title: {
    flex: 1,
    fontSize: typography.md,
    fontWeight: typography.semibold,
    color: colors.text,
  },
  
  content: {
    alignItems: 'center',
  },
  
  value: {
    fontSize: typography.xxxl,
    fontWeight: typography.bold,
    color: colors.steps,
  },
  
  target: {
    fontSize: typography.sm,
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  
  progressContainer: {
    width: '100%',
  },
  
  progressBar: {
    width: '100%',
    height: 6,
    backgroundColor: colors.surfaceLight,
    borderRadius: 3,
  },
  
  progressFill: {
    height: '100%',
    backgroundColor: colors.steps,
    borderRadius: 3,
  },
});
