import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography, commonStyles } from '../utils/theme';

export default function AgendaScreen({ navigation }) {
  const today = new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.title}>Agenda</Text>
            <Text style={styles.date}>{today}</Text>
          </View>
          <TouchableOpacity style={styles.addButton}>
            <Ionicons name="add" size={24} color={colors.surface} />
          </TouchableOpacity>
        </View>

        {/* Quick Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statValue}>0</Text>
            <Text style={styles.statLabel}>Tasks Completed</Text>
          </View>
          
          <View style={styles.statCard}>
            <Text style={styles.statValue}>0</Text>
            <Text style={styles.statLabel}>Habits Done</Text>
          </View>
          
          <View style={styles.statCard}>
            <Text style={styles.statValue}>0</Text>
            <Text style={styles.statLabel}>Goals Progress</Text>
          </View>
        </View>

        {/* Today's Schedule */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Today's Schedule</Text>
          
          <View style={styles.timeSlots}>
            {[
              { time: '7:00 AM', task: 'Morning meditation', completed: false },
              { time: '8:00 AM', task: 'Healthy breakfast', completed: false },
              { time: '12:00 PM', task: 'Lunch break', completed: false },
              { time: '6:00 PM', task: 'Evening workout', completed: false },
              { time: '9:00 PM', task: 'Wind down routine', completed: false },
            ].map((item, index) => (
              <TouchableOpacity key={index} style={styles.timeSlot}>
                <View style={styles.timeContainer}>
                  <Text style={styles.timeText}>{item.time}</Text>
                </View>
                <View style={styles.taskContainer}>
                  <View style={styles.taskContent}>
                    <Text style={[
                      styles.taskText,
                      item.completed && styles.taskTextCompleted
                    ]}>
                      {item.task}
                    </Text>
                  </View>
                  <TouchableOpacity style={styles.checkButton}>
                    <Ionicons
                      name={item.completed ? "checkmark-circle" : "ellipse-outline"}
                      size={24}
                      color={item.completed ? colors.success : colors.textLight}
                    />
                  </TouchableOpacity>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Wellness Goals */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Wellness Goals</Text>
          
          <View style={styles.goalsContainer}>
            {[
              { goal: 'Drink 8 glasses of water', progress: 0, target: 8, unit: 'glasses' },
              { goal: 'Walk 10,000 steps', progress: 0, target: 10000, unit: 'steps' },
              { goal: 'Sleep 8 hours', progress: 0, target: 8, unit: 'hours' },
            ].map((item, index) => (
              <View key={index} style={styles.goalCard}>
                <Text style={styles.goalText}>{item.goal}</Text>
                <View style={styles.goalProgress}>
                  <Text style={styles.goalProgressText}>
                    {item.progress} / {item.target} {item.unit}
                  </Text>
                  <View style={styles.progressBar}>
                    <View style={[
                      styles.progressFill,
                      { width: `${(item.progress / item.target) * 100}%` }
                    ]} />
                  </View>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          
          <View style={styles.actionsGrid}>
            {[
              { name: 'Add Task', icon: 'add-circle', color: colors.primary },
              { name: 'Log Mood', icon: 'happy', color: colors.mood },
              { name: 'Start Timer', icon: 'timer', color: colors.secondary },
              { name: 'View Calendar', icon: 'calendar', color: colors.steps },
            ].map((action) => (
              <TouchableOpacity key={action.name} style={styles.actionCard}>
                <Ionicons name={action.icon} size={24} color={action.color} />
                <Text style={styles.actionName}>{action.name}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  
  content: {
    flex: 1,
  },
  
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
  },
  
  title: {
    fontSize: typography.xxl,
    fontWeight: typography.bold,
    color: colors.text,
  },
  
  date: {
    fontSize: typography.sm,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    gap: spacing.sm,
  },
  
  statCard: {
    flex: 1,
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: spacing.md,
    alignItems: 'center',
    ...commonStyles.card,
  },
  
  statValue: {
    fontSize: typography.xl,
    fontWeight: typography.bold,
    color: colors.text,
  },
  
  statLabel: {
    fontSize: typography.xs,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: spacing.xs,
  },
  
  section: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  
  sectionTitle: {
    fontSize: typography.lg,
    fontWeight: typography.semibold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  
  timeSlots: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    ...commonStyles.card,
  },
  
  timeSlot: {
    flexDirection: 'row',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.surfaceLight,
  },
  
  timeContainer: {
    width: 80,
    justifyContent: 'center',
  },
  
  timeText: {
    fontSize: typography.sm,
    color: colors.textSecondary,
    fontWeight: typography.medium,
  },
  
  taskContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  
  taskContent: {
    flex: 1,
    marginLeft: spacing.md,
  },
  
  taskText: {
    fontSize: typography.md,
    color: colors.text,
  },
  
  taskTextCompleted: {
    textDecorationLine: 'line-through',
    color: colors.textSecondary,
  },
  
  checkButton: {
    padding: spacing.xs,
  },
  
  goalsContainer: {
    gap: spacing.sm,
  },
  
  goalCard: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: spacing.md,
    ...commonStyles.card,
  },
  
  goalText: {
    fontSize: typography.md,
    fontWeight: typography.medium,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  
  goalProgress: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  
  goalProgressText: {
    fontSize: typography.sm,
    color: colors.textSecondary,
    marginRight: spacing.md,
  },
  
  progressBar: {
    flex: 1,
    height: 6,
    backgroundColor: colors.surfaceLight,
    borderRadius: 3,
  },
  
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 3,
  },
  
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
  },
  
  actionCard: {
    width: '47%',
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: spacing.md,
    alignItems: 'center',
    ...commonStyles.card,
  },
  
  actionName: {
    fontSize: typography.sm,
    fontWeight: typography.medium,
    color: colors.text,
    marginTop: spacing.sm,
  },
});
