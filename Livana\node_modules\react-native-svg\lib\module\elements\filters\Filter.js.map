{"version": 3, "names": ["React", "RNSVGFilter", "<PERSON><PERSON><PERSON>", "Filter", "displayName", "defaultProps", "x", "y", "width", "height", "filterUnits", "primitiveUnits", "render", "id", "props", "filterProps", "name", "createElement", "_extends", "ref", "refMethod", "children"], "sourceRoot": "../../../../src", "sources": ["elements/filters/Filter.tsx"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,OAAOC,WAAW,MAAM,oCAAoC;AAE5D,OAAOC,KAAK,MAAM,UAAU;AAa5B,eAAe,MAAMC,MAAM,SAASD,KAAK,CAAc;EACrD,OAAOE,WAAW,GAAG,QAAQ;EAE7B,OAAOC,YAAY,GAAwC;IACzDC,CAAC,EAAE,MAAM;IACTC,CAAC,EAAE,MAAM;IACTC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,mBAAmB;IAChCC,cAAc,EAAE;EAClB,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEC,EAAE;MAAEP,CAAC;MAAEC,CAAC;MAAEC,KAAK;MAAEC,MAAM;MAAEC,WAAW;MAAEC;IAAe,CAAC,GAAG,IAAI,CAACG,KAAK;IAE3E,MAAMC,WAAW,GAAG;MAClBC,IAAI,EAAEH,EAAE;MACRP,CAAC;MACDC,CAAC;MACDC,KAAK;MACLC,MAAM;MACNC,WAAW;MACXC;IACF,CAAC;IACD,oBACEX,KAAA,CAAAiB,aAAA,CAAChB,WAAW,EAAAiB,QAAA;MACVC,GAAG,EAAGA,GAAG,IAAK,IAAI,CAACC,SAAS,CAACD,GAAsC;IAAE,GACjEJ,WAAW,GACd,IAAI,CAACD,KAAK,CAACO,QACD,CAAC;EAElB;AACF", "ignoreList": []}