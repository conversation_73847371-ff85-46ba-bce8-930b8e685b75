import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  SafeAreaView,
  Dimensions,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { getTheme } from '../utils/theme';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const themeDisplayNames = {
  sage: 'Sage Green',
  dark: 'Dark Mode',
  light: 'Clean Light',
  pink: 'Pink Blossom',
  navy: 'Navy Professional',
};

const themeDescriptions = {
  sage: 'Calm and natural with sage green accents',
  dark: 'Easy on the eyes with dark backgrounds',
  light: 'Clean and minimal professional design',
  pink: 'Warm and energetic with pink tones',
  navy: 'Professional and trustworthy navy blue',
};

const themeIcons = {
  sage: 'leaf',
  dark: 'moon',
  light: 'sunny',
  pink: 'heart',
  navy: 'business',
};

export default function ThemeSelector({ visible, onClose }) {
  const { currentThemeName, availableThemes, changeTheme } = useTheme();
  const theme = useTheme().currentTheme;

  const handleThemeSelect = async (themeName) => {
    await changeTheme(themeName);
    onClose();
  };

  const renderThemeOption = (themeName) => {
    const themeColors = getTheme(themeName);
    const isSelected = currentThemeName === themeName;

    return (
      <TouchableOpacity
        key={themeName}
        style={[
          styles.themeOption,
          { 
            backgroundColor: theme.surface,
            borderColor: isSelected ? theme.primary : theme.border,
            borderWidth: isSelected ? 2 : 1,
          }
        ]}
        onPress={() => handleThemeSelect(themeName)}
      >
        <View style={styles.themePreview}>
          {/* Color preview circles */}
          <View style={styles.colorPreview}>
            <View style={[styles.colorCircle, { backgroundColor: themeColors.primary }]} />
            <View style={[styles.colorCircle, { backgroundColor: themeColors.secondary }]} />
            <View style={[styles.colorCircle, { backgroundColor: themeColors.background }]} />
          </View>
          
          {/* Theme icon */}
          <View style={[styles.themeIconContainer, { backgroundColor: themeColors.primary + '20' }]}>
            <Ionicons 
              name={themeIcons[themeName]} 
              size={24} 
              color={themeColors.primary} 
            />
          </View>
        </View>

        <View style={styles.themeInfo}>
          <View style={styles.themeHeader}>
            <Text style={[styles.themeName, { color: theme.text }]}>
              {themeDisplayNames[themeName]}
            </Text>
            {isSelected && (
              <Ionicons name="checkmark-circle" size={20} color={theme.primary} />
            )}
          </View>
          <Text style={[styles.themeDescription, { color: theme.textSecondary }]}>
            {themeDescriptions[themeName]}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={[styles.modalOverlay, { backgroundColor: theme.overlay }]}>
        <SafeAreaView style={styles.modalContainer}>
          <View style={[styles.modalContent, { backgroundColor: theme.surface }]}>
            {/* Header */}
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.text }]}>
                Choose Theme
              </Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={onClose}
              >
                <Ionicons name="close" size={24} color={theme.textSecondary} />
              </TouchableOpacity>
            </View>

            {/* Theme options */}
            <ScrollView 
              style={styles.themeList}
              showsVerticalScrollIndicator={false}
            >
              {availableThemes.map(renderThemeOption)}
            </ScrollView>

            {/* Footer */}
            <View style={styles.modalFooter}>
              <Text style={[styles.footerText, { color: theme.textLight }]}>
                Your theme preference will be saved automatically
              </Text>
            </View>
          </View>
        </SafeAreaView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
  },

  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },

  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: Platform.OS === 'ios' ? '85%' : '80%',
    minHeight: screenHeight < 700 ? '70%' : '60%',
    paddingBottom: Platform.OS === 'ios' ? 34 : 0, // Safe area for iOS
  },
  
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  
  closeButton: {
    padding: 8,
    minWidth: 44,
    minHeight: 44,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  themeList: {
    flex: 1,
    padding: 20,
  },
  
  themeOption: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: 80,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  
  themePreview: {
    marginRight: 16,
    alignItems: 'center',
  },
  
  colorPreview: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  
  colorCircle: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginHorizontal: 1,
  },
  
  themeIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  themeInfo: {
    flex: 1,
  },
  
  themeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  
  themeName: {
    fontSize: 16,
    fontWeight: '600',
  },
  
  themeDescription: {
    fontSize: 13,
    lineHeight: 18,
  },
  
  modalFooter: {
    padding: 20,
    alignItems: 'center',
  },
  
  footerText: {
    fontSize: 12,
    textAlign: 'center',
  },
});
