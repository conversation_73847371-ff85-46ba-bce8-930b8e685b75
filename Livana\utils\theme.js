// Livana App Theme Configuration
// Professional wellness-focused color palettes

// Theme definitions
const themes = {
  // Original Sage Green Theme
  sage: {
    // Primary colors
    primary: '#8FBC8F',        // Sage green
    primaryLight: '#A8D3A8',   // Light sage
    primaryDark: '#6B8E6B',    // Dark sage

    // Secondary colors
    secondary: '#FFB07A',      // Peach
    secondaryLight: '#FFCBA4', // Light peach
    secondaryDark: '#E6956B',  // Dark peach

    // Neutral colors
    background: '#FEFEFE',     // Off-white
    surface: '#FFFFFF',        // Pure white
    surfaceLight: '#F8F8F8',   // Very light gray
    surfaceElevated: '#FFFFFF', // Elevated surface

    // Text colors
    text: '#2C2C2C',          // Dark gray
    textSecondary: '#6B6B6B',  // Medium gray
    textLight: '#9B9B9B',      // Light gray
    textInverse: '#FFFFFF',    // White text for dark backgrounds

    // Status colors
    success: '#4CAF50',        // Green
    warning: '#FF9800',        // Orange
    error: '#F44336',          // Red
    info: '#2196F3',           // Blue

    // Widget colors
    water: '#87CEEB',          // Sky blue
    mood: '#DDA0DD',           // Plum
    sleep: '#9370DB',          // Medium slate blue
    steps: '#32CD32',          // Lime green
    habits: '#FFD700',         // Gold

    // Transparent overlays
    overlay: 'rgba(0, 0, 0, 0.5)',
    overlayLight: 'rgba(0, 0, 0, 0.2)',

    // Border colors
    border: '#E0E0E0',
    borderLight: '#F0F0F0',
    borderDark: '#CCCCCC',
  },

  // Dark Theme
  dark: {
    // Primary colors
    primary: '#4ECDC4',        // Teal
    primaryLight: '#6EDDD6',   // Light teal
    primaryDark: '#3BA99C',    // Dark teal

    // Secondary colors
    secondary: '#FF6B6B',      // Coral
    secondaryLight: '#FF8E8E', // Light coral
    secondaryDark: '#E55555',  // Dark coral

    // Neutral colors
    background: '#1A1A1A',     // Very dark gray
    surface: '#2D2D2D',        // Dark gray
    surfaceLight: '#3A3A3A',   // Medium dark gray
    surfaceElevated: '#404040', // Elevated surface

    // Text colors
    text: '#FFFFFF',           // White
    textSecondary: '#B0B0B0',  // Light gray
    textLight: '#808080',      // Medium gray
    textInverse: '#1A1A1A',    // Dark text for light backgrounds

    // Status colors
    success: '#4CAF50',        // Green
    warning: '#FFA726',        // Orange
    error: '#EF5350',          // Red
    info: '#42A5F5',           // Blue

    // Widget colors
    water: '#64B5F6',          // Blue
    mood: '#BA68C8',           // Purple
    sleep: '#7986CB',          // Indigo
    steps: '#66BB6A',          // Green
    habits: '#FFB74D',         // Amber

    // Transparent overlays
    overlay: 'rgba(0, 0, 0, 0.7)',
    overlayLight: 'rgba(0, 0, 0, 0.4)',

    // Border colors
    border: '#404040',
    borderLight: '#505050',
    borderDark: '#303030',
  },

  // Light Theme
  light: {
    // Primary colors
    primary: '#6366F1',        // Indigo
    primaryLight: '#818CF8',   // Light indigo
    primaryDark: '#4F46E5',    // Dark indigo

    // Secondary colors
    secondary: '#10B981',      // Emerald
    secondaryLight: '#34D399', // Light emerald
    secondaryDark: '#059669',  // Dark emerald

    // Neutral colors
    background: '#FFFFFF',     // Pure white
    surface: '#FAFAFA',        // Very light gray
    surfaceLight: '#F5F5F5',   // Light gray
    surfaceElevated: '#FFFFFF', // Elevated surface

    // Text colors
    text: '#1F2937',           // Very dark gray
    textSecondary: '#6B7280',  // Medium gray
    textLight: '#9CA3AF',      // Light gray
    textInverse: '#FFFFFF',    // White text

    // Status colors
    success: '#10B981',        // Emerald
    warning: '#F59E0B',        // Amber
    error: '#EF4444',          // Red
    info: '#3B82F6',           // Blue

    // Widget colors
    water: '#06B6D4',          // Cyan
    mood: '#8B5CF6',           // Violet
    sleep: '#6366F1',          // Indigo
    steps: '#10B981',          // Emerald
    habits: '#F59E0B',         // Amber

    // Transparent overlays
    overlay: 'rgba(0, 0, 0, 0.5)',
    overlayLight: 'rgba(0, 0, 0, 0.1)',

    // Border colors
    border: '#E5E7EB',
    borderLight: '#F3F4F6',
    borderDark: '#D1D5DB',
  },

  // Pink Theme
  pink: {
    // Primary colors
    primary: '#EC4899',        // Pink
    primaryLight: '#F472B6',   // Light pink
    primaryDark: '#DB2777',    // Dark pink

    // Secondary colors
    secondary: '#8B5CF6',      // Purple
    secondaryLight: '#A78BFA', // Light purple
    secondaryDark: '#7C3AED',  // Dark purple

    // Neutral colors
    background: '#FDF2F8',     // Very light pink
    surface: '#FFFFFF',        // Pure white
    surfaceLight: '#FCE7F3',   // Light pink tint
    surfaceElevated: '#FFFFFF', // Elevated surface

    // Text colors
    text: '#1F2937',           // Very dark gray
    textSecondary: '#6B7280',  // Medium gray
    textLight: '#9CA3AF',      // Light gray
    textInverse: '#FFFFFF',    // White text

    // Status colors
    success: '#10B981',        // Emerald
    warning: '#F59E0B',        // Amber
    error: '#EF4444',          // Red
    info: '#3B82F6',           // Blue

    // Widget colors
    water: '#06B6D4',          // Cyan
    mood: '#EC4899',           // Pink (primary)
    sleep: '#8B5CF6',          // Purple
    steps: '#10B981',          // Emerald
    habits: '#F59E0B',         // Amber

    // Transparent overlays
    overlay: 'rgba(0, 0, 0, 0.5)',
    overlayLight: 'rgba(236, 72, 153, 0.1)',

    // Border colors
    border: '#F3E8FF',
    borderLight: '#FAE8FF',
    borderDark: '#E879F9',
  },

  // Navy Blue Theme
  navy: {
    // Primary colors
    primary: '#1E3A8A',        // Navy blue
    primaryLight: '#3B82F6',   // Blue
    primaryDark: '#1E40AF',    // Dark blue

    // Secondary colors
    secondary: '#0891B2',      // Cyan
    secondaryLight: '#06B6D4', // Light cyan
    secondaryDark: '#0E7490',  // Dark cyan

    // Neutral colors
    background: '#F8FAFC',     // Very light blue-gray
    surface: '#FFFFFF',        // Pure white
    surfaceLight: '#F1F5F9',   // Light blue-gray
    surfaceElevated: '#FFFFFF', // Elevated surface

    // Text colors
    text: '#0F172A',           // Very dark blue-gray
    textSecondary: '#475569',  // Medium blue-gray
    textLight: '#94A3B8',      // Light blue-gray
    textInverse: '#FFFFFF',    // White text

    // Status colors
    success: '#10B981',        // Emerald
    warning: '#F59E0B',        // Amber
    error: '#EF4444',          // Red
    info: '#3B82F6',           // Blue

    // Widget colors
    water: '#0891B2',          // Cyan (secondary)
    mood: '#8B5CF6',           // Violet
    sleep: '#1E3A8A',          // Navy (primary)
    steps: '#10B981',          // Emerald
    habits: '#F59E0B',         // Amber

    // Transparent overlays
    overlay: 'rgba(0, 0, 0, 0.5)',
    overlayLight: 'rgba(30, 58, 138, 0.1)',

    // Border colors
    border: '#E2E8F0',
    borderLight: '#F1F5F9',
    borderDark: '#CBD5E1',
  },
};

// Default theme colors (will be overridden by ThemeContext)
export const colors = themes.sage;

// Enhanced spacing system for professional design
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,

  // Component-specific spacing
  componentPadding: 16,
  sectionSpacing: 24,
  screenPadding: 20,
  cardPadding: 20,
  buttonPadding: 16,
};

// Enhanced border radius for modern design
export const borderRadius = {
  xs: 2,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  xxl: 20,
  round: 50,

  // Component-specific radius
  button: 12,
  card: 16,
  input: 8,
  modal: 20,
};

// Professional typography system
export const typography = {
  // Font sizes with better hierarchy
  xs: 11,
  sm: 13,
  md: 15,
  lg: 17,
  xl: 19,
  xxl: 22,
  xxxl: 28,
  display: 34,

  // Font weights
  light: '300',
  regular: '400',
  medium: '500',
  semibold: '600',
  bold: '700',
  extrabold: '800',

  // Line heights for better readability
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },

  // Letter spacing
  letterSpacing: {
    tight: -0.5,
    normal: 0,
    wide: 0.5,
    wider: 1,
  },
};

// Enhanced shadow system for depth and professionalism
export const shadows = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  xs: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
    elevation: 1,
  },
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.15,
    shadowRadius: 15,
    elevation: 8,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 20 },
    shadowOpacity: 0.2,
    shadowRadius: 25,
    elevation: 12,
  },

  // Component-specific shadows
  card: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
  },
  button: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  modal: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
};

// Professional common component styles (use with theme context)
// Note: Temporarily disabled due to hardcoded colors - use individual styles with theme context
// TODO: Re-implement commonStyles with dynamic theme support

// Theme switching utility
export const getTheme = (themeName) => {
  return themes[themeName] || themes.sage;
};

// Available theme names
export const themeNames = Object.keys(themes);

// Export themes object
export { themes };
