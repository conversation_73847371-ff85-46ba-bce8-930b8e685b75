// Livana App Theme Configuration
// Calm, wellness-focused color palette

export const colors = {
  // Primary colors
  primary: '#8FBC8F',        // Sage green
  primaryLight: '#A8D3A8',   // Light sage
  primaryDark: '#6B8E6B',    // Dark sage
  
  // Secondary colors
  secondary: '#FFB07A',      // Peach
  secondaryLight: '#FFCBA4', // Light peach
  secondaryDark: '#E6956B',  // Dark peach
  
  // Neutral colors
  background: '#FEFEFE',     // Off-white
  surface: '#FFFFFF',        // Pure white
  surfaceLight: '#F8F8F8',   // Very light gray
  
  // Text colors
  text: '#2C2C2C',          // Dark gray
  textSecondary: '#6B6B6B',  // Medium gray
  textLight: '#9B9B9B',      // Light gray
  
  // Status colors
  success: '#4CAF50',        // Green
  warning: '#FF9800',        // Orange
  error: '#F44336',          // Red
  info: '#2196F3',           // Blue
  
  // Widget colors
  water: '#87CEEB',          // Sky blue
  mood: '#DDA0DD',           // Plum
  sleep: '#9370DB',          // Medium slate blue
  steps: '#32CD32',          // Lime green
  habits: '#FFD700',         // Gold
  
  // Transparent overlays
  overlay: 'rgba(0, 0, 0, 0.5)',
  overlayLight: 'rgba(0, 0, 0, 0.2)',
};

export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const borderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  round: 50,
};

export const typography = {
  // Font sizes
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
  xxxl: 32,
  
  // Font weights
  light: '300',
  regular: '400',
  medium: '500',
  semibold: '600',
  bold: '700',
};

export const shadows = {
  sm: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,
    elevation: 1,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.23,
    shadowRadius: 2.62,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.30,
    shadowRadius: 4.65,
    elevation: 8,
  },
};

// Common component styles
export const commonStyles = {
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  
  card: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    marginVertical: spacing.sm,
    ...shadows.sm,
  },
  
  button: {
    backgroundColor: colors.primary,
    borderRadius: borderRadius.md,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  buttonText: {
    color: colors.surface,
    fontSize: typography.md,
    fontWeight: typography.semibold,
  },
  
  input: {
    borderWidth: 1,
    borderColor: colors.textLight,
    borderRadius: borderRadius.md,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    fontSize: typography.md,
    backgroundColor: colors.surface,
  },
  
  title: {
    fontSize: typography.xxl,
    fontWeight: typography.bold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  
  subtitle: {
    fontSize: typography.lg,
    fontWeight: typography.semibold,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  
  body: {
    fontSize: typography.md,
    color: colors.textSecondary,
    lineHeight: 24,
  },
  
  caption: {
    fontSize: typography.sm,
    color: colors.textLight,
  },
};
