{"version": 3, "names": ["_reactNative", "require", "responder<PERSON><PERSON><PERSON>", "Object", "keys", "PanResponder", "create", "panHandlers", "numResponderKeys", "length", "extractResponder", "o", "props", "ref", "onPress", "disabled", "onPressIn", "onPressOut", "onLongPress", "delayPressIn", "delayPressOut", "delayLongPress", "pointerEvents", "responsible", "i", "key", "value", "hasTouchableProperty", "onResponderMove", "touchableHandleResponderMove", "onResponderGrant", "touchableHandleResponderGrant", "onResponderRelease", "touchableHandleResponderRelease", "onResponderTerminate", "touchableHandleResponderTerminate", "onStartShouldSetResponder", "touchableHandleStartShouldSetResponder", "onResponderTerminationRequest", "touchableHandleResponderTerminationRequest"], "sourceRoot": "../../../../src", "sources": ["lib/extract/extractResponder.ts"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAOA,MAAMC,aAAa,GAAGC,MAAM,CAACC,IAAI,CAACC,yBAAY,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;AACtE,MAAMC,gBAAgB,GAAGN,aAAa,CAACO,MAAM;AAE9B,SAASC,gBAAgBA,CACtCC,CAAiB;AACjB;AACAC,KAA4C,EAC5CC,GAA2B,EAC3B;EACA,MAAM;IACJC,OAAO;IACPC,QAAQ;IACRC,SAAS;IACTC,UAAU;IACVC,WAAW;IACXC,YAAY;IACZC,aAAa;IACbC,cAAc;IACdC;EACF,CAAC,GAAGV,KAAK;EAET,IAAIW,WAAW,GAAG,KAAK;EACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,gBAAgB,EAAEgB,CAAC,EAAE,EAAE;IACzC,MAAMC,GAAG,GAAGvB,aAAa,CAACsB,CAAC,CAAC;IAC5B,MAAME,KAAK,GAAGd,KAAK,CAACa,GAAG,CAAC;IACxB,IAAIC,KAAK,EAAE;MACTH,WAAW,GAAG,IAAI;MAClBZ,CAAC,CAACc,GAAG,CAAC,GAAGC,KAAK;IAChB;EACF;EAEA,IAAIJ,aAAa,EAAE;IACjBX,CAAC,CAACW,aAAa,GAAGA,aAAa;EACjC;EAEA,MAAMK,oBAAoB,GACxBZ,QAAQ,IAAI,IAAI,IAChBD,OAAO,IACPE,SAAS,IACTC,UAAU,IACVC,WAAW,IACXC,YAAY,IACZC,aAAa,IACbC,cAAc;EAEhB,IAAIM,oBAAoB,EAAE;IACxBJ,WAAW,GAAG,IAAI;IAClBZ,CAAC,CAACiB,eAAe,GAAGf,GAAG,CAACgB,4BAA4B;IACpDlB,CAAC,CAACmB,gBAAgB,GAAGjB,GAAG,CAACkB,6BAA6B;IACtDpB,CAAC,CAACqB,kBAAkB,GAAGnB,GAAG,CAACoB,+BAA+B;IAC1DtB,CAAC,CAACuB,oBAAoB,GAAGrB,GAAG,CAACsB,iCAAiC;IAC9DxB,CAAC,CAACyB,yBAAyB,GAAGvB,GAAG,CAACwB,sCAAsC;IACxE1B,CAAC,CAAC2B,6BAA6B,GAC7BzB,GAAG,CAAC0B,0CAA0C;EAClD;EAEA,IAAIhB,WAAW,EAAE;IACfZ,CAAC,CAACY,WAAW,GAAG,IAAI;EACtB;AACF", "ignoreList": []}