{"version": 3, "names": ["meetOrSliceTypes", "exports", "meet", "slice", "none", "alignEnum", "reduce", "prev", "name", "spacesRegExp", "extractViewBox", "props", "viewBox", "preserveAspectRatio", "params", "Array", "isArray", "trim", "replace", "split", "map", "Number", "length", "some", "isNaN", "console", "warn", "modes", "align", "meetOrSlice", "minX", "minY", "vbWidth", "vbHeight"], "sourceRoot": "../../../../src", "sources": ["lib/extract/extractViewBox.ts"], "mappings": ";;;;;;;;AAEO,MAAMA,gBAEZ,GAAAC,OAAA,CAAAD,gBAAA,GAAG;EACFE,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,CAAC;EACRC,IAAI,EAAE;AACR,CAAC;AAEM,MAAMC,SAAsC,GAAAJ,OAAA,CAAAI,SAAA,GAAG,CACpD,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,MAAM,CACP,CAACC,MAAM,CAAC,CAACC,IAAiC,EAAEC,IAAI,KAAK;EACpDD,IAAI,CAACC,IAAI,CAAC,GAAGA,IAAI;EACjB,OAAOD,IAAI;AACb,CAAC,EAAE,CAAC,CAAC,CAAC;AAEN,MAAME,YAAY,GAAG,KAAK;AAEX,SAASC,cAAcA,CAACC,KAGtC,EAAE;EACD,MAAM;IAAEC,OAAO;IAAEC;EAAoB,CAAC,GAAGF,KAAK;EAE9C,IAAI,CAACC,OAAO,EAAE;IACZ,OAAO,IAAI;EACb;EAEA,MAAME,MAAM,GAAG,CACbC,KAAK,CAACC,OAAO,CAACJ,OAAO,CAAC,GAClBA,OAAO,GACPA,OAAO,CAACK,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACC,KAAK,CAACV,YAAY,CAAC,EACzDW,GAAG,CAACC,MAAM,CAAC;EAEb,IAAIP,MAAM,CAACQ,MAAM,KAAK,CAAC,IAAIR,MAAM,CAACS,IAAI,CAACC,KAAK,CAAC,EAAE;IAC7CC,OAAO,CAACC,IAAI,CAAC,yBAAyB,GAAGd,OAAO,CAAC;IACjD,OAAO,IAAI;EACb;EAEA,MAAMe,KAAK,GAAGd,mBAAmB,GAC7BA,mBAAmB,CAACI,IAAI,CAAC,CAAC,CAACE,KAAK,CAACV,YAAY,CAAC,GAC9C,EAAE;EACN,MAAMmB,KAAK,GAAGD,KAAK,CAAC,CAAC,CAAC;EACtB,MAAME,WAAW,GAAGF,KAAK,CAAC,CAAC,CAAC;EAE5B,OAAO;IACLG,IAAI,EAAEhB,MAAM,CAAC,CAAC,CAAC;IACfiB,IAAI,EAAEjB,MAAM,CAAC,CAAC,CAAC;IACfkB,OAAO,EAAElB,MAAM,CAAC,CAAC,CAAC;IAClBmB,QAAQ,EAAEnB,MAAM,CAAC,CAAC,CAAC;IACnBc,KAAK,EAAEvB,SAAS,CAACuB,KAAK,CAAC,IAAI,UAAU;IACrCC,WAAW,EAAE7B,gBAAgB,CAAC6B,WAAW,CAAC,IAAI;EAChD,CAAC;AACH", "ignoreList": []}