// Sample data for Livana app

export const motivationalQuotes = [
  "Every small step counts towards your wellness journey.",
  "Your body is your temple. Keep it pure and clean for the soul to reside in.",
  "Health is not about the weight you lose, but about the life you gain.",
  "Take care of your body. It's the only place you have to live.",
  "Wellness is the complete integration of body, mind, and spirit.",
  "The groundwork for all happiness is good health.",
  "To keep the body in good health is a duty... otherwise we shall not be able to keep our mind strong and clear.",
  "A healthy outside starts from the inside.",
  "Your health is an investment, not an expense.",
  "The first wealth is health.",
];

export const lifestyleQuizQuestions = [
  {
    id: 1,
    question: "How would you describe your current activity level?",
    type: "single",
    options: [
      { value: "sedentary", label: "Sedentary (little to no exercise)" },
      { value: "light", label: "Lightly active (light exercise 1-3 days/week)" },
      { value: "moderate", label: "Moderately active (moderate exercise 3-5 days/week)" },
      { value: "very", label: "Very active (hard exercise 6-7 days/week)" },
      { value: "extra", label: "Extra active (very hard exercise, physical job)" },
    ],
  },
  {
    id: 2,
    question: "What are your primary wellness goals? (Select all that apply)",
    type: "multiple",
    options: [
      { value: "weight_loss", label: "Weight loss" },
      { value: "muscle_gain", label: "Muscle gain" },
      { value: "stress_reduction", label: "Stress reduction" },
      { value: "better_sleep", label: "Better sleep" },
      { value: "more_energy", label: "More energy" },
      { value: "mindfulness", label: "Mindfulness & mental clarity" },
      { value: "nutrition", label: "Better nutrition" },
      { value: "habit_building", label: "Building healthy habits" },
    ],
  },
  {
    id: 3,
    question: "How many hours of sleep do you typically get per night?",
    type: "single",
    options: [
      { value: "less_5", label: "Less than 5 hours" },
      { value: "5_6", label: "5-6 hours" },
      { value: "6_7", label: "6-7 hours" },
      { value: "7_8", label: "7-8 hours" },
      { value: "8_9", label: "8-9 hours" },
      { value: "more_9", label: "More than 9 hours" },
    ],
  },
  {
    id: 4,
    question: "How would you rate your current stress level?",
    type: "scale",
    min: 1,
    max: 10,
    labels: { 1: "Very Low", 10: "Very High" },
  },
  {
    id: 5,
    question: "What time of day do you feel most energetic?",
    type: "single",
    options: [
      { value: "early_morning", label: "Early morning (5-8 AM)" },
      { value: "morning", label: "Morning (8-11 AM)" },
      { value: "midday", label: "Midday (11 AM-2 PM)" },
      { value: "afternoon", label: "Afternoon (2-5 PM)" },
      { value: "evening", label: "Evening (5-8 PM)" },
      { value: "night", label: "Night (8-11 PM)" },
    ],
  },
];

export const healthyRecipes = [
  {
    id: 1,
    title: "Green Goddess Smoothie",
    category: "Breakfast",
    prepTime: "5 min",
    difficulty: "Easy",
    calories: 280,
    ingredients: [
      "1 banana",
      "1 cup spinach",
      "1/2 avocado",
      "1 cup almond milk",
      "1 tbsp chia seeds",
      "1 tsp honey",
    ],
    instructions: [
      "Add all ingredients to a blender",
      "Blend until smooth",
      "Pour into a glass and enjoy immediately",
    ],
    tags: ["vegan", "gluten-free", "high-protein"],
  },
  {
    id: 2,
    title: "Mediterranean Quinoa Bowl",
    category: "Lunch",
    prepTime: "20 min",
    difficulty: "Medium",
    calories: 420,
    ingredients: [
      "1 cup cooked quinoa",
      "1/2 cucumber, diced",
      "1/2 cup cherry tomatoes",
      "1/4 cup red onion",
      "1/4 cup feta cheese",
      "2 tbsp olive oil",
      "1 tbsp lemon juice",
      "Fresh herbs",
    ],
    instructions: [
      "Cook quinoa according to package instructions",
      "Dice vegetables",
      "Mix olive oil and lemon juice for dressing",
      "Combine all ingredients and toss with dressing",
    ],
    tags: ["vegetarian", "gluten-free", "mediterranean"],
  },
  {
    id: 3,
    title: "Baked Salmon with Herbs",
    category: "Dinner",
    prepTime: "25 min",
    difficulty: "Medium",
    calories: 350,
    ingredients: [
      "4 oz salmon fillet",
      "1 tbsp olive oil",
      "1 tsp dried herbs",
      "1/2 lemon",
      "Salt and pepper",
      "Steamed vegetables",
    ],
    instructions: [
      "Preheat oven to 400°F",
      "Season salmon with herbs, salt, and pepper",
      "Drizzle with olive oil and lemon juice",
      "Bake for 12-15 minutes",
      "Serve with steamed vegetables",
    ],
    tags: ["high-protein", "omega-3", "low-carb"],
  },
];

export const meditationSessions = [
  {
    id: 1,
    title: "Morning Mindfulness",
    duration: "10 min",
    category: "Mindfulness",
    description: "Start your day with intention and clarity",
    audioFile: "morning_mindfulness.mp3", // Placeholder
    difficulty: "Beginner",
  },
  {
    id: 2,
    title: "Stress Relief",
    duration: "15 min",
    category: "Stress Relief",
    description: "Release tension and find inner peace",
    audioFile: "stress_relief.mp3", // Placeholder
    difficulty: "Intermediate",
  },
  {
    id: 3,
    title: "Sleep Preparation",
    duration: "20 min",
    category: "Sleep",
    description: "Prepare your mind and body for restful sleep",
    audioFile: "sleep_preparation.mp3", // Placeholder
    difficulty: "Beginner",
  },
  {
    id: 4,
    title: "Body Scan",
    duration: "25 min",
    category: "Body Awareness",
    description: "Connect with your body through mindful awareness",
    audioFile: "body_scan.mp3", // Placeholder
    difficulty: "Intermediate",
  },
];

export const breathingExercises = [
  {
    id: 1,
    name: "4-7-8 Breathing",
    description: "Inhale for 4, hold for 7, exhale for 8",
    duration: "5 min",
    steps: [
      "Sit comfortably with your back straight",
      "Exhale completely through your mouth",
      "Inhale through your nose for 4 counts",
      "Hold your breath for 7 counts",
      "Exhale through your mouth for 8 counts",
      "Repeat 3-4 times",
    ],
  },
  {
    id: 2,
    name: "Box Breathing",
    description: "Equal counts for inhale, hold, exhale, hold",
    duration: "10 min",
    steps: [
      "Sit in a comfortable position",
      "Inhale for 4 counts",
      "Hold for 4 counts",
      "Exhale for 4 counts",
      "Hold empty for 4 counts",
      "Repeat for 10 minutes",
    ],
  },
];

export const defaultHabits = [
  {
    id: 1,
    name: "Drink 8 glasses of water",
    category: "Health",
    frequency: "daily",
    target: 8,
    unit: "glasses",
    icon: "water",
    color: "#87CEEB",
  },
  {
    id: 2,
    name: "Take 10,000 steps",
    category: "Fitness",
    frequency: "daily",
    target: 10000,
    unit: "steps",
    icon: "walk",
    color: "#32CD32",
  },
  {
    id: 3,
    name: "Meditate",
    category: "Mindfulness",
    frequency: "daily",
    target: 10,
    unit: "minutes",
    icon: "leaf",
    color: "#DDA0DD",
  },
  {
    id: 4,
    name: "Read",
    category: "Personal Growth",
    frequency: "daily",
    target: 30,
    unit: "minutes",
    icon: "book",
    color: "#FFD700",
  },
  {
    id: 5,
    name: "Sleep 8 hours",
    category: "Health",
    frequency: "daily",
    target: 8,
    unit: "hours",
    icon: "moon",
    color: "#9370DB",
  },
];

export const magicModeActivities = [
  "Take 5 deep breaths and notice how you feel",
  "Write down 3 things you're grateful for today",
  "Do 10 jumping jacks to boost your energy",
  "Drink a glass of water mindfully",
  "Step outside and take in some fresh air",
  "Stretch your arms and shoulders",
  "Send a kind message to someone you care about",
  "Tidy up your immediate space",
  "Listen to your favorite song",
  "Practice a 2-minute meditation",
  "Do some gentle neck rolls",
  "Write down one positive affirmation",
  "Take a photo of something beautiful around you",
  "Do 5 push-ups or modified push-ups",
  "Smile at yourself in the mirror",
];
