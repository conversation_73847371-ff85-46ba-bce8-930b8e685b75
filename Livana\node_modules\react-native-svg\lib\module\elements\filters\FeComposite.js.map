{"version": 3, "names": ["React", "RNSVGFeComposite", "extractFeComposite", "extractFilter", "FilterPrimitive", "FeComposite", "displayName", "defaultProps", "defaultPrimitiveProps", "k1", "k2", "k3", "k4", "render", "createElement", "_extends", "ref", "refMethod", "props"], "sourceRoot": "../../../../src", "sources": ["elements/filters/FeComposite.tsx"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,OAAOC,gBAAgB,MAAM,yCAAyC;AACtE,SACEC,kBAAkB,EAClBC,aAAa,QACR,iCAAiC;AAExC,OAAOC,eAAe,MAAM,mBAAmB;AAoB/C,eAAe,MAAMC,WAAW,SAASD,eAAe,CAAmB;EACzE,OAAOE,WAAW,GAAG,aAAa;EAElC,OAAOC,YAAY,GAAG;IACpB,GAAG,IAAI,CAACC,qBAAqB;IAC7BC,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE;EACN,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,oBACEb,KAAA,CAAAc,aAAA,CAACb,gBAAgB,EAAAc,QAAA;MACfC,GAAG,EAAGA,GAAG,IACP,IAAI,CAACC,SAAS,CAACD,GAA2C;IAC3D,GACGb,aAAa,CAAC,IAAI,CAACe,KAAK,CAAC,EACzBhB,kBAAkB,CAAC,IAAI,CAACgB,KAAK,CAAC,CACnC,CAAC;EAEN;AACF", "ignoreList": []}