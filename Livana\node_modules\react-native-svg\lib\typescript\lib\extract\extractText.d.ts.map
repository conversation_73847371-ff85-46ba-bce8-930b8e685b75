{"version": 3, "file": "extractText.d.ts", "sourceRoot": "", "sources": ["../../../../src/lib/extract/extractText.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,OAAO,CAAC;AAC3C,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAI/B,OAAO,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,SAAS,CAAC;AAiDvD,UAAU,SAAS;IACjB,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,UAAU,CAAC,EAAE,UAAU,CAAC;IACxB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,QAAQ,CAAC,EAAE,UAAU,CAAC;IACtB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,aAAa,CAAC,EAAE,UAAU,CAAC;IAC3B,WAAW,CAAC,EAAE,UAAU,CAAC;IACzB,OAAO,CAAC,EAAE,UAAU,CAAC;IACrB,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAC7B,oBAAoB,CAAC,EAAE,MAAM,CAAC;IAC9B,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAC/B,IAAI,CAAC,EAAE,MAAM,CAAC;CACf;AAED,wBAAgB,WAAW,CAAC,KAAK,EAAE,SAAS;;;;;EAyC3C;AAID,wBAAgB,QAAQ,CAAC,mBAAmB,EAAE,aAAa,QAE1D;AAED,MAAM,MAAM,SAAS,GACjB,CAAC,SAAS,GAAG,MAAM,GAAG,MAAM,GAAG,aAAa,GAAG,KAAK,CAAC,YAAY,CAAC,GAClE,SAAS,EAAE,CAAC;AAUhB,MAAM,MAAM,SAAS,GAAG;IACtB,CAAC,CAAC,EAAE,WAAW,CAAC;IAChB,CAAC,CAAC,EAAE,WAAW,CAAC;IAChB,EAAE,CAAC,EAAE,WAAW,CAAC;IACjB,EAAE,CAAC,EAAE,WAAW,CAAC;IACjB,MAAM,CAAC,EAAE,WAAW,CAAC;IACrB,QAAQ,CAAC,EAAE,SAAS,CAAC;IACrB,UAAU,CAAC,EAAE,UAAU,CAAC;IACxB,aAAa,CAAC,EAAE,UAAU,CAAC;IAC3B,aAAa,CAAC,EAAE,UAAU,CAAC;IAC3B,iBAAiB,CAAC,EAAE,MAAM,CAAC;CAC5B,GAAG,SAAS,CAAC;AAEd,MAAM,CAAC,OAAO,UAAU,WAAW,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO;;;;;;;;;;;;;;;;;;EAuCvE"}