{"version": 3, "names": ["PixelRatio", "getAssetByID", "svgDataUriPattern", "resolveAssetUri", "source", "src", "asset", "Error", "width", "height", "scale", "scales", "length", "preferredScale", "get", "reduce", "prev", "curr", "Math", "abs", "scaleSuffix", "uri", "httpServerLocation", "name", "type", "Array", "isArray", "_src", "match", "prefix", "svg", "encodedSvg", "encodeURIComponent"], "sourceRoot": "../../../src", "sources": ["lib/resolveAssetUri.ts"], "mappings": "AAAA,SAEEA,UAAU,QAEL,cAAc;AACrB;AACA,SAASC,YAAY,QAAQ,wCAAwC;AAcrE,MAAMC,iBAAiB,GAAG,mCAAmC;;AAE7D;AACA,OAAO,SAASC,eAAeA,CAC7BC,MAAiD,EACP;EAC1C,IAAIC,GAAsC,GAAG,CAAC,CAAC;EAC/C,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;IAC9B;IACA,MAAME,KAA2B,GAAGL,YAAY,CAACG,MAAM,CAAC;IACxD,IAAIE,KAAK,IAAI,IAAI,EAAE;MACjB,MAAM,IAAIC,KAAK,CACb,yBAAyBH,MAAM,kEACjC,CAAC;IACH;IACAC,GAAG,GAAG;MACJG,KAAK,EAAEF,KAAK,CAACE,KAAK;MAClBC,MAAM,EAAEH,KAAK,CAACG,MAAM;MACpBC,KAAK,EAAEJ,KAAK,CAACK,MAAM,CAAC,CAAC;IACvB,CAAC;IACD,IAAIL,KAAK,CAACK,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3B,MAAMC,cAAc,GAAGb,UAAU,CAACc,GAAG,CAAC,CAAC;MACvC;MACAT,GAAG,CAACK,KAAK,GAAGJ,KAAK,CAACK,MAAM,CAACI,MAAM,CAAC,CAACC,IAAI,EAAEC,IAAI,KACzCC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAGJ,cAAc,CAAC,GAAGK,IAAI,CAACC,GAAG,CAACH,IAAI,GAAGH,cAAc,CAAC,GAC7DI,IAAI,GACJD,IACN,CAAC;IACH;IACA,MAAMI,WAAW,GAAGf,GAAG,CAACK,KAAK,KAAK,CAAC,GAAG,IAAIL,GAAG,CAACK,KAAK,GAAG,GAAG,EAAE;IAC3DL,GAAG,CAACgB,GAAG,GAAGf,KAAK,GACX,GAAGA,KAAK,CAACgB,kBAAkB,IAAIhB,KAAK,CAACiB,IAAI,GAAGH,WAAW,IAAId,KAAK,CAACkB,IAAI,EAAE,GACvE,EAAE;EACR,CAAC,MAAM,IAAI,OAAOpB,MAAM,KAAK,QAAQ,EAAE;IACrCC,GAAG,CAACgB,GAAG,GAAGjB,MAAM;EAClB,CAAC,MAAM,IACLA,MAAM,IACN,CAACqB,KAAK,CAACC,OAAO,CAACtB,MAAM,CAAC,IACtB,OAAOA,MAAM,CAACiB,GAAG,KAAK,QAAQ,EAC9B;IACAhB,GAAG,CAACgB,GAAG,GAAGjB,MAAM,CAACiB,GAAG;EACtB;EAEA,IAAIhB,GAAG,CAACgB,GAAG,EAAE;IAAA,IAAAM,IAAA;IACX,MAAMC,KAAK,IAAAD,IAAA,GAAGtB,GAAG,cAAAsB,IAAA,gBAAAA,IAAA,GAAHA,IAAA,CAAKN,GAAG,cAAAM,IAAA,uBAARA,IAAA,CAAUC,KAAK,CAAC1B,iBAAiB,CAAC;IAChD;IACA,IAAI0B,KAAK,EAAE;MACT,MAAM,GAAGC,MAAM,EAAEC,GAAG,CAAC,GAAGF,KAAK;MAC7B,MAAMG,UAAU,GAAGC,kBAAkB,CAACF,GAAG,CAAC;MAC1CzB,GAAG,CAACgB,GAAG,GAAG,GAAGQ,MAAM,GAAGE,UAAU,EAAE;MAClC,OAAO1B,GAAG;IACZ;EACF;EACA,OAAOA,GAAG;AACZ", "ignoreList": []}