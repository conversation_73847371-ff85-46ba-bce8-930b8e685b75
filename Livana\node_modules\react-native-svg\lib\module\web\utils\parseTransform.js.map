{"version": 3, "names": ["transformsArrayToProps", "parseTransformProp", "transform", "props", "transformArray", "push", "stringifyTransformProps", "Array", "isArray", "join", "stringifiedProps", "length", "undefined", "transformProps", "translate", "translateX", "translateY", "scale", "scaleX", "scaleY", "rotation", "skewX", "skewY"], "sourceRoot": "../../../../src", "sources": ["web/utils/parseTransform.ts"], "mappings": "AACA,SACEA,sBAAsB,QAEjB,oCAAoC;AAG3C,OAAO,SAASC,kBAAkBA,CAChCC,SAAsC,EACtCC,KAAiB,EACjB;EACA,MAAMC,cAAwB,GAAG,EAAE;EAEnCD,KAAK,IAAIC,cAAc,CAACC,IAAI,CAAC,GAAGC,uBAAuB,CAACH,KAAK,CAAC,CAAC;EAE/D,IAAII,KAAK,CAACC,OAAO,CAACN,SAAS,CAAC,EAAE;IAC5B,IAAI,OAAOA,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MACpCE,cAAc,CAACC,IAAI,CAAC,UAAUH,SAAS,CAACO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IACvD,CAAC,MAAM;MACL,MAAMC,gBAAgB,GAAGV,sBAAsB;MAC7C;MACAE,SACF,CAAC;MACDE,cAAc,CAACC,IAAI,CAAC,GAAGC,uBAAuB,CAACI,gBAAgB,CAAC,CAAC;IACnE;EACF,CAAC,MAAM,IAAI,OAAOR,SAAS,KAAK,QAAQ,EAAE;IACxCE,cAAc,CAACC,IAAI,CAACH,SAAS,CAAC;EAChC;EAEA,OAAOE,cAAc,CAACO,MAAM,GAAGP,cAAc,CAACK,IAAI,CAAC,GAAG,CAAC,GAAGG,SAAS;AACrE;AAEA,OAAO,SAASN,uBAAuBA,CAACO,cAA8B,EAAE;EACtE,MAAMT,cAAc,GAAG,EAAE;EACzB,IAAIS,cAAc,CAACC,SAAS,IAAI,IAAI,EAAE;IACpCV,cAAc,CAACC,IAAI,CAAC,aAAaQ,cAAc,CAACC,SAAS,GAAG,CAAC;EAC/D;EACA,IAAID,cAAc,CAACE,UAAU,IAAI,IAAI,IAAIF,cAAc,CAACG,UAAU,IAAI,IAAI,EAAE;IAC1EZ,cAAc,CAACC,IAAI,CACjB,aAAaQ,cAAc,CAACE,UAAU,IAAI,CAAC,KACzCF,cAAc,CAACG,UAAU,IAAI,CAAC,GAElC,CAAC;EACH;EACA,IAAIH,cAAc,CAACI,KAAK,IAAI,IAAI,EAAE;IAChCb,cAAc,CAACC,IAAI,CAAC,SAASQ,cAAc,CAACI,KAAK,GAAG,CAAC;EACvD;EACA,IAAIJ,cAAc,CAACK,MAAM,IAAI,IAAI,IAAIL,cAAc,CAACM,MAAM,IAAI,IAAI,EAAE;IAClEf,cAAc,CAACC,IAAI,CACjB,SAASQ,cAAc,CAACK,MAAM,IAAI,CAAC,KAAKL,cAAc,CAACM,MAAM,IAAI,CAAC,GACpE,CAAC;EACH;EACA;EACA,IAAIN,cAAc,CAACO,QAAQ,IAAI,IAAI,EAAE;IACnChB,cAAc,CAACC,IAAI,CAAC,UAAUQ,cAAc,CAACO,QAAQ,GAAG,CAAC;EAC3D;EACA,IAAIP,cAAc,CAACQ,KAAK,IAAI,IAAI,EAAE;IAChCjB,cAAc,CAACC,IAAI,CAAC,SAASQ,cAAc,CAACQ,KAAK,GAAG,CAAC;EACvD;EACA,IAAIR,cAAc,CAACS,KAAK,IAAI,IAAI,EAAE;IAChClB,cAAc,CAACC,IAAI,CAAC,SAASQ,cAAc,CAACS,KAAK,GAAG,CAAC;EACvD;EACA,OAAOlB,cAAc;AACvB", "ignoreList": []}