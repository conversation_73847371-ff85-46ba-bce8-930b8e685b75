# Livana Development Guide

## 🚀 Quick Start

### Running the App
```bash
cd Livana
npm start
```

Then:
- Press `w` for web version
- Press `i` for iOS simulator
- Press `a` for Android emulator
- Scan QR code with Expo Go app

### Project Structure Overview

```
Livana/
├── App.js                    # Main app entry point
├── navigation/
│   ├── AppNavigator.js       # Main navigation setup
│   └── OnboardingNavigator.js # Onboarding flow
├── screens/
│   ├── onboarding/           # First-time user experience
│   │   ├── WelcomeScreen.js
│   │   ├── LifestyleQuizScreen.js
│   │   ├── GoalSettingScreen.js
│   │   ├── PersonalizationScreen.js
│   │   └── OnboardingCompleteScreen.js
│   ├── DashboardScreen.js    # Main dashboard
│   ├── FitnessScreen.js      # Workout tracking
│   ├── NutritionScreen.js    # Meal logging
│   ├── MindfulnessScreen.js  # Meditation & journaling
│   └── AgendaScreen.js       # Daily planning
├── components/
│   └── widgets/              # Dashboard widgets
│       ├── StepsWidget.js
│       ├── WaterWidget.js
│       ├── MoodWidget.js
│       ├── SleepWidget.js
│       ├── HabitsWidget.js
│       ├── QuoteWidget.js
│       ├── WeightWidget.js
│       ├── FitnessWidget.js
│       └── AgendaWidget.js
├── data/
│   ├── storage.js            # AsyncStorage utilities
│   └── sampleData.js         # Mock data
├── utils/
│   └── theme.js              # Design system
└── assets/                   # Images and icons
```

## 🎨 Design System

### Theme Usage
```javascript
import { colors, spacing, typography, shadows } from '../utils/theme';

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.background,
    padding: spacing.lg,
  },
  title: {
    fontSize: typography.xl,
    fontWeight: typography.bold,
    color: colors.text,
  },
  card: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: spacing.md,
    ...shadows.sm,
  },
});
```

### Color Palette
- `colors.primary` - Sage green (#8FBC8F)
- `colors.secondary` - Peach (#FFB07A)
- `colors.background` - Off-white (#FEFEFE)
- `colors.surface` - Pure white (#FFFFFF)
- `colors.text` - Dark gray (#2C2C2C)

### Widget Colors
- `colors.water` - Sky blue (#87CEEB)
- `colors.mood` - Plum (#DDA0DD)
- `colors.sleep` - Medium slate blue (#9370DB)
- `colors.steps` - Lime green (#32CD32)
- `colors.habits` - Gold (#FFD700)

## 📱 Component Patterns

### Widget Structure
All widgets follow this pattern:
```javascript
export default function ExampleWidget({ data, onUpdate, navigation }) {
  return (
    <TouchableOpacity style={styles.container}>
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          <Ionicons name="icon-name" size={24} color={colors.primary} />
        </View>
        <Text style={styles.title}>Widget Title</Text>
        <Ionicons name="chevron-forward" size={16} color={colors.textLight} />
      </View>
      
      <View style={styles.content}>
        {/* Widget-specific content */}
      </View>
      
      <View style={styles.footer}>
        {/* Optional footer content */}
      </View>
    </TouchableOpacity>
  );
}
```

### Screen Structure
```javascript
export default function ExampleScreen({ navigation }) {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title}>Screen Title</Text>
        </View>
        
        {/* Screen content */}
      </ScrollView>
    </SafeAreaView>
  );
}
```

## 💾 Data Management

### Storage Functions
```javascript
import { saveDailyData, getDailyData } from '../data/storage';

// Save data for today
const today = new Date().toISOString().split('T')[0];
await saveDailyData(today, { steps: 5000, water: 3 });

// Get today's data
const todayData = await getDailyData(today);
```

### Available Storage Functions
- `saveUserProfile(profile)` / `getUserProfile()`
- `saveDailyData(date, data)` / `getDailyData(date)`
- `saveHabits(habits)` / `getHabits()`
- `saveWorkout(workout)` / `getWorkouts()`
- `saveMeal(meal)` / `getMeals()`
- `saveJournalEntry(entry)` / `getJournalEntries()`

## 🧭 Navigation

### Adding New Screens
1. Create screen component in appropriate folder
2. Add to navigator in `navigation/AppNavigator.js`
3. Update tab bar icons if needed

### Navigation Between Screens
```javascript
// Navigate to another screen
navigation.navigate('ScreenName', { param: 'value' });

// Go back
navigation.goBack();

// Reset navigation stack
navigation.reset({
  index: 0,
  routes: [{ name: 'ScreenName' }],
});
```

## 🔧 Development Tips

### Hot Reload
- Save any file to trigger hot reload
- Shake device or press Ctrl+M for dev menu
- Use `console.log()` for debugging

### Testing on Device
1. Install Expo Go app
2. Scan QR code from terminal
3. App will load on your device

### Common Issues
- **Metro bundler issues**: Clear cache with `npx expo start -c`
- **Package conflicts**: Check package.json versions
- **Navigation errors**: Ensure all screens are properly imported

### Adding New Dependencies
```bash
# Install package
npm install package-name

# For React Native specific packages
npx expo install package-name
```

## 📝 Next Steps

### Immediate Improvements
1. Add error boundaries for better error handling
2. Implement proper loading states
3. Add form validation
4. Create unit tests

### Feature Additions
1. Notification system
2. Data export functionality
3. Settings screen
4. User profile management
5. Health app integrations

### Performance Optimizations
1. Implement lazy loading for screens
2. Optimize image loading
3. Add memoization for expensive calculations
4. Implement virtual lists for large datasets

## 🐛 Debugging

### Common Debug Commands
```bash
# Clear Metro cache
npx expo start -c

# Reset project
npx expo start --clear

# Check for issues
npx expo doctor
```

### Useful Debug Tools
- React Native Debugger
- Flipper
- Expo Dev Tools
- Chrome DevTools (for web)

---

Happy coding! 🚀
