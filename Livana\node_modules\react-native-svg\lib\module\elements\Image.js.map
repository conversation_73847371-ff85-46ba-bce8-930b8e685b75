{"version": 3, "names": ["React", "Image", "alignEnum", "meetOrSliceTypes", "withoutXY", "<PERSON><PERSON><PERSON>", "RNSVGImage", "spacesRegExp", "SvgImage", "displayName", "defaultProps", "x", "y", "width", "height", "preserveAspectRatio", "render", "props", "xlinkHref", "href", "onLoad", "modes", "trim", "split", "align", "meetOrSlice", "imageProps", "src", "resolveAssetSource", "uri", "createElement", "_extends", "ref", "refMethod"], "sourceRoot": "../../../src", "sources": ["elements/Image.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAO9B,SAASC,KAAK,QAAQ,cAAc;AACpC,SAASC,SAAS,EAAEC,gBAAgB,QAAQ,+BAA+B;AAC3E,SAASC,SAAS,QAAQ,6BAA6B;AAEvD,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,UAAU,MAAM,gCAAgC;AAEvD,MAAMC,YAAY,GAAG,KAAK;AAc1B,eAAe,MAAMC,QAAQ,SAASH,KAAK,CAAa;EACtD,OAAOI,WAAW,GAAG,OAAO;EAE5B,OAAOC,YAAY,GAAG;IACpBC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,mBAAmB,EAAE;EACvB,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEC;IAAM,CAAC,GAAG,IAAI;IACtB,MAAM;MACJF,mBAAmB;MACnBJ,CAAC;MACDC,CAAC;MACDC,KAAK;MACLC,MAAM;MACNI,SAAS;MACTC,IAAI,GAAGD,SAAS;MAChBE;IACF,CAAC,GAAGH,KAAK;IACT,MAAMI,KAAK,GAAGN,mBAAmB,GAC7BA,mBAAmB,CAACO,IAAI,CAAC,CAAC,CAACC,KAAK,CAAChB,YAAY,CAAC,GAC9C,EAAE;IACN,MAAMiB,KAAK,GAAGH,KAAK,CAAC,CAAC,CAAC;IACtB,MAAMI,WAA2D,GAC/DJ,KAAK,CAAC,CAAC,CAAC;IACV,MAAMK,UAAU,GAAG;MACjBf,CAAC;MACDC,CAAC;MACDC,KAAK;MACLC,MAAM;MACNM,MAAM;MACNK,WAAW,EAAEtB,gBAAgB,CAACsB,WAAW,CAAC,IAAI,CAAC;MAC/CD,KAAK,EAAEtB,SAAS,CAACsB,KAAK,CAAC,IAAI,UAAU;MACrCG,GAAG,EAAE,CAACR,IAAI,GACN,IAAI,GACJlB,KAAK,CAAC2B,kBAAkB,CACtB,OAAOT,IAAI,KAAK,QAAQ,GAAG;QAAEU,GAAG,EAAEV;MAAK,CAAC,GAAGA,IAC7C;IACN,CAAC;IACD,oBACEnB,KAAA,CAAA8B,aAAA,CAACxB,UAAU,EAAAyB,QAAA;MACTC,GAAG,EAAGA,GAAG,IAAK,IAAI,CAACC,SAAS,CAACD,GAAwC;IAAE,GACnE5B,SAAS,CAAC,IAAI,EAAEa,KAAK,CAAC,EACtBS,UAAU,CACf,CAAC;EAEN;AACF", "ignoreList": []}