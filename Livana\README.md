# Livana - Holistic Lifestyle & Health Tracker

A comprehensive React Native mobile app designed to help users lead a balanced, mindful life through personalized wellness tracking and gentle guidance.

## 🌿 Features

### Core Functionality
- **Onboarding Flow**: Personalized lifestyle quiz and goal setting
- **Dashboard**: Customizable widgets for daily wellness tracking
- **Smart Coaching**: AI-powered insights and daily check-ins
- **Magic Mode**: Random wellness activities for instant motivation

### Tracking Modules
- **Fitness**: Workout logging, progress tracking, and goal monitoring
- **Nutrition**: Meal logging, healthy recipes, and calorie tracking
- **Mindfulness**: Guided meditations, breathing exercises, and journaling
- **Agenda**: Daily planning with habit tracking and wellness goals

### Key Widgets
- Daily steps counter with Google Fit/Apple Health integration
- Interactive water intake tracker
- Mood logging with notes and emoji selection
- Sleep hours tracking with quality assessment
- Custom habit progress monitoring
- Daily motivational quotes
- Weight tracking with progress visualization
- Fitness summary and workout history
- Today's agenda with task management

## 🎨 Design Philosophy

- **Calm Color Palette**: <PERSON> green (#8FBC8F), peach (#FFB07A), and off-white (#FEFEFE)
- **Mindful UX**: Gentle interactions and encouraging messaging
- **Accessibility**: Clear typography and intuitive navigation
- **Customization**: User-controlled dashboard widgets and preferences

## 🏗️ Technical Architecture

### Project Structure
```
Livana/
├── components/
│   └── widgets/          # Reusable dashboard widgets
├── screens/
│   ├── onboarding/       # Welcome, quiz, goals, personalization
│   ├── DashboardScreen.js
│   ├── FitnessScreen.js
│   ├── NutritionScreen.js
│   ├── MindfulnessScreen.js
│   └── AgendaScreen.js
├── navigation/           # React Navigation setup
├── data/
│   ├── storage.js        # AsyncStorage utilities
│   └── sampleData.js     # Mock data and content
├── utils/
│   └── theme.js          # Design system and styling
└── assets/               # Images and icons
```

### Technology Stack
- **Framework**: React Native with Expo
- **Navigation**: React Navigation (Bottom Tabs + Stack)
- **Storage**: AsyncStorage for local data persistence
- **UI Components**: Custom components with consistent theming
- **Icons**: Expo Vector Icons
- **State Management**: React Hooks (useState, useEffect)

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- Expo CLI
- iOS Simulator or Android Emulator (optional)

### Installation
1. Clone the repository
2. Navigate to the project directory
3. Install dependencies:
   ```bash
   npm install
   ```

### Running the App
```bash
# Start the development server
npm start

# Run on specific platforms
npm run ios      # iOS Simulator
npm run android  # Android Emulator
npm run web      # Web browser
```

### Development
- Scan the QR code with Expo Go app for mobile testing
- Press 'w' in the terminal to open web version
- Hot reload is enabled for rapid development

## 📱 App Flow

### First-Time Users
1. **Welcome Screen**: Introduction to Livana's features
2. **Lifestyle Quiz**: 5 questions to understand user preferences
3. **Goal Setting**: Choose from categorized wellness goals
4. **Personalization**: Basic info and preferences
5. **Completion**: Welcome to personalized dashboard

### Daily Usage
1. **Dashboard**: Central hub with customizable widgets
2. **Quick Actions**: Log mood, add water, start meditation
3. **Magic Mode**: Get random wellness activities
4. **Progress Tracking**: View trends and achievements
5. **Smart Coaching**: Receive personalized insights

## 🎯 Planned Features

### Phase 2 Enhancements
- **Cloud Sync**: Firebase integration for data backup
- **Social Features**: Share progress with friends
- **Advanced Analytics**: Weekly/monthly wellness reports
- **Notification System**: Smart reminders and encouragement
- **Integration**: Google Fit, Apple Health, wearable devices

### Phase 3 Expansions
- **AI Coaching**: OpenAI integration for conversational guidance
- **Community**: User groups and challenges
- **Professional**: Connect with wellness coaches
- **Marketplace**: Premium content and guided programs

## 🎨 Design System

### Colors
- **Primary**: Sage Green (#8FBC8F) - Calming, natural
- **Secondary**: Peach (#FFB07A) - Warm, encouraging
- **Background**: Off-white (#FEFEFE) - Clean, minimal
- **Widget Colors**: Water blue, mood purple, sleep indigo, etc.

### Typography
- **Sizes**: 12px to 32px scale
- **Weights**: Light (300) to Bold (700)
- **Hierarchy**: Clear distinction between headers, body, and captions

### Spacing
- **Scale**: 4px, 8px, 16px, 24px, 32px, 48px
- **Consistent**: Applied throughout components
- **Responsive**: Adapts to different screen sizes

## 🔧 Development Notes

### Widget System
Each widget is a self-contained component with:
- Consistent header with icon and title
- Interactive content area
- Footer with status or actions
- Standardized styling and behavior

### Data Management
- Local-first approach with AsyncStorage
- Structured data models for different content types
- Easy migration path to cloud storage
- Offline-first functionality

### Navigation Structure
- Bottom tabs for main sections
- Stack navigation for detailed flows
- Modal presentations for quick actions
- Deep linking support ready

## 📄 License

This project is part of a development showcase. All rights reserved.

## 🤝 Contributing

This is a demonstration project. For production use, consider:
- Adding comprehensive testing
- Implementing proper error handling
- Adding accessibility features
- Optimizing performance
- Adding analytics and monitoring

---

**Livana** - Your journey to holistic wellness starts here. 🌱
