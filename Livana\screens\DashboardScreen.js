import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography } from '../utils/theme';
import { getUserProfile, getDailyData, getDashboardWidgets } from '../data/storage';
import { motivationalQuotes, magicModeActivities } from '../data/sampleData';

// Import widget components
import StepsWidget from '../components/widgets/StepsWidget';
import WaterWidget from '../components/widgets/WaterWidget';
import MoodWidget from '../components/widgets/MoodWidget';
import SleepWidget from '../components/widgets/SleepWidget';
import HabitsWidget from '../components/widgets/HabitsWidget';
import QuoteWidget from '../components/widgets/QuoteWidget';
import WeightWidget from '../components/widgets/WeightWidget';
import FitnessWidget from '../components/widgets/FitnessWidget';
import AgendaWidget from '../components/widgets/AgendaWidget';

export default function DashboardScreen({ navigation }) {
  const [userProfile, setUserProfile] = useState(null);
  const [dailyData, setDailyData] = useState({});
  const [enabledWidgets, setEnabledWidgets] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [currentQuote, setCurrentQuote] = useState('');
  const [magicActivity, setMagicActivity] = useState('');

  const today = new Date().toISOString().split('T')[0];

  useEffect(() => {
    loadData();
    setDailyQuote();
  }, []);

  const loadData = async () => {
    try {
      const profile = await getUserProfile();
      const todayData = await getDailyData(today);
      const widgets = await getDashboardWidgets();
      
      setUserProfile(profile);
      setDailyData(todayData);
      setEnabledWidgets(widgets);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  };

  const setDailyQuote = () => {
    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];
    setCurrentQuote(randomQuote);
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setDailyQuote();
    setRefreshing(false);
  };

  const handleMagicMode = () => {
    const randomActivity = magicModeActivities[Math.floor(Math.random() * magicModeActivities.length)];
    setMagicActivity(randomActivity);
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    const name = userProfile?.name || 'there';
    
    if (hour < 12) return `Good morning, ${name}!`;
    if (hour < 17) return `Good afternoon, ${name}!`;
    return `Good evening, ${name}!`;
  };

  const renderWidget = (widgetType) => {
    const commonProps = {
      data: dailyData,
      onUpdate: loadData,
      navigation,
    };

    switch (widgetType) {
      case 'steps':
        return <StepsWidget key="steps" {...commonProps} />;
      case 'water':
        return <WaterWidget key="water" {...commonProps} />;
      case 'mood':
        return <MoodWidget key="mood" {...commonProps} />;
      case 'sleep':
        return <SleepWidget key="sleep" {...commonProps} />;
      case 'habits':
        return <HabitsWidget key="habits" {...commonProps} />;
      case 'quote':
        return <QuoteWidget key="quote" quote={currentQuote} />;
      case 'weight':
        return <WeightWidget key="weight" {...commonProps} />;
      case 'fitness':
        return <FitnessWidget key="fitness" {...commonProps} />;
      case 'agenda':
        return <AgendaWidget key="agenda" {...commonProps} />;
      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.greetingContainer}>
            <Text style={styles.greeting}>{getGreeting()}</Text>
            <Text style={styles.date}>
              {new Date().toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </Text>
          </View>
          
          <TouchableOpacity style={styles.profileButton}>
            <Ionicons name="person-circle" size={32} color={colors.primary} />
          </TouchableOpacity>
        </View>

        {/* Vision Statement */}
        {userProfile?.visionStatement && (
          <View style={styles.visionContainer}>
            <Text style={styles.visionText}>"{userProfile.visionStatement}"</Text>
          </View>
        )}

        {/* Magic Mode Button */}
        <TouchableOpacity style={styles.magicButton} onPress={handleMagicMode}>
          <View style={styles.magicButtonContent}>
            <Ionicons name="sparkles" size={24} color={colors.surface} />
            <Text style={styles.magicButtonText}>Magic Mode</Text>
          </View>
          <Text style={styles.magicButtonSubtext}>Get a random wellness activity</Text>
        </TouchableOpacity>

        {/* Magic Activity Display */}
        {magicActivity && (
          <View style={styles.magicActivityContainer}>
            <View style={styles.magicActivityHeader}>
              <Ionicons name="star" size={20} color={colors.secondary} />
              <Text style={styles.magicActivityTitle}>Your Magic Activity</Text>
            </View>
            <Text style={styles.magicActivityText}>{magicActivity}</Text>
            <TouchableOpacity
              style={styles.dismissButton}
              onPress={() => setMagicActivity('')}
            >
              <Text style={styles.dismissButtonText}>Done</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Widgets Grid */}
        <View style={styles.widgetsContainer}>
          {enabledWidgets.map((widgetType) => renderWidget(widgetType))}
        </View>

        {/* Customize Widgets Button */}
        <TouchableOpacity style={styles.customizeButton}>
          <Ionicons name="settings" size={20} color={colors.primary} />
          <Text style={styles.customizeButtonText}>Customize Widgets</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  
  content: {
    flex: 1,
  },
  
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
  },
  
  greetingContainer: {
    flex: 1,
  },
  
  greeting: {
    fontSize: typography.xl,
    fontWeight: typography.bold,
    color: colors.text,
  },
  
  date: {
    fontSize: typography.sm,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  
  profileButton: {
    padding: spacing.sm,
  },
  
  visionContainer: {
    backgroundColor: colors.primaryLight + '20',
    marginHorizontal: spacing.lg,
    marginVertical: spacing.md,
    padding: spacing.md,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
  },
  
  visionText: {
    fontSize: typography.sm,
    color: colors.text,
    fontStyle: 'italic',
    textAlign: 'center',
  },
  
  magicButton: {
    backgroundColor: colors.secondary,
    marginHorizontal: spacing.lg,
    marginVertical: spacing.sm,
    borderRadius: 16,
    padding: spacing.lg,
    alignItems: 'center',
  },
  
  magicButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  
  magicButtonText: {
    fontSize: typography.lg,
    fontWeight: typography.semibold,
    color: colors.surface,
    marginLeft: spacing.sm,
  },
  
  magicButtonSubtext: {
    fontSize: typography.sm,
    color: colors.surface,
    opacity: 0.9,
  },
  
  magicActivityContainer: {
    backgroundColor: colors.surface,
    marginHorizontal: spacing.lg,
    marginVertical: spacing.sm,
    borderRadius: 12,
    padding: spacing.lg,
    borderWidth: 2,
    borderColor: colors.secondary,
  },
  
  magicActivityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  
  magicActivityTitle: {
    fontSize: typography.md,
    fontWeight: typography.semibold,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  
  magicActivityText: {
    fontSize: typography.md,
    color: colors.text,
    lineHeight: 22,
    marginBottom: spacing.md,
  },
  
  dismissButton: {
    alignSelf: 'flex-end',
    backgroundColor: colors.secondary,
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.md,
    borderRadius: 16,
  },
  
  dismissButtonText: {
    fontSize: typography.sm,
    color: colors.surface,
    fontWeight: typography.medium,
  },
  
  widgetsContainer: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.lg,
  },
  
  customizeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: spacing.lg,
    marginBottom: spacing.xl,
    paddingVertical: spacing.md,
    borderWidth: 1,
    borderColor: colors.primary,
    borderRadius: 8,
    borderStyle: 'dashed',
  },
  
  customizeButtonText: {
    fontSize: typography.md,
    color: colors.primary,
    marginLeft: spacing.sm,
  },
});
