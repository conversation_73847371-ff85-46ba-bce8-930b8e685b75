import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography, commonStyles } from '../utils/theme';

export default function MindfulnessScreen({ navigation }) {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Mindfulness</Text>
          <TouchableOpacity style={styles.journalButton}>
            <Ionicons name="journal" size={24} color={colors.surface} />
          </TouchableOpacity>
        </View>

        {/* Daily Check-in */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Daily Check-in</Text>
          <TouchableOpacity style={styles.checkinCard}>
            <View style={styles.checkinContent}>
              <Ionicons name="heart" size={24} color={colors.mood} />
              <View style={styles.checkinText}>
                <Text style={styles.checkinTitle}>How are you feeling today?</Text>
                <Text style={styles.checkinSubtitle}>Take a moment to reflect</Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.textLight} />
          </TouchableOpacity>
        </View>

        {/* Meditation Sessions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Guided Meditations</Text>
          
          {[
            { title: 'Morning Mindfulness', duration: '10 min', category: 'Beginner' },
            { title: 'Stress Relief', duration: '15 min', category: 'Intermediate' },
            { title: 'Sleep Preparation', duration: '20 min', category: 'Sleep' },
          ].map((session, index) => (
            <TouchableOpacity key={index} style={styles.sessionCard}>
              <View style={styles.sessionIcon}>
                <Ionicons name="play-circle" size={32} color={colors.primary} />
              </View>
              <View style={styles.sessionInfo}>
                <Text style={styles.sessionTitle}>{session.title}</Text>
                <Text style={styles.sessionDetails}>
                  {session.duration} • {session.category}
                </Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color={colors.textLight} />
            </TouchableOpacity>
          ))}
        </View>

        {/* Breathing Exercises */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Breathing Exercises</Text>
          
          <View style={styles.breathingGrid}>
            {[
              { name: '4-7-8 Breathing', duration: '5 min', icon: 'leaf' },
              { name: 'Box Breathing', duration: '10 min', icon: 'square' },
            ].map((exercise) => (
              <TouchableOpacity key={exercise.name} style={styles.breathingCard}>
                <View style={[styles.breathingIcon, { backgroundColor: colors.mood + '20' }]}>
                  <Ionicons name={exercise.icon} size={24} color={colors.mood} />
                </View>
                <Text style={styles.breathingName}>{exercise.name}</Text>
                <Text style={styles.breathingDuration}>{exercise.duration}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Journaling */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Journaling</Text>
          
          <TouchableOpacity style={styles.journalingCard}>
            <View style={styles.journalingContent}>
              <Ionicons name="create" size={24} color={colors.secondary} />
              <View style={styles.journalingText}>
                <Text style={styles.journalingTitle}>Today's Journal</Text>
                <Text style={styles.journalingSubtitle}>Reflect on your thoughts and feelings</Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.textLight} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.journalingCard}>
            <View style={styles.journalingContent}>
              <Ionicons name="mail" size={24} color={colors.primary} />
              <View style={styles.journalingText}>
                <Text style={styles.journalingTitle}>Letter to Future Me</Text>
                <Text style={styles.journalingSubtitle}>Write to yourself in the future</Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.textLight} />
          </TouchableOpacity>
        </View>

        {/* Mindfulness Stats */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Your Progress</Text>
          
          <View style={styles.statsContainer}>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>0</Text>
              <Text style={styles.statLabel}>Minutes meditated</Text>
            </View>
            
            <View style={styles.statCard}>
              <Text style={styles.statValue}>0</Text>
              <Text style={styles.statLabel}>Days streak</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  
  content: {
    flex: 1,
  },
  
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
  },
  
  title: {
    fontSize: typography.xxl,
    fontWeight: typography.bold,
    color: colors.text,
  },
  
  journalButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.mood,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  section: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  
  sectionTitle: {
    fontSize: typography.lg,
    fontWeight: typography.semibold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  
  checkinCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: spacing.md,
    ...commonStyles.card,
  },
  
  checkinContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  
  checkinText: {
    marginLeft: spacing.md,
    flex: 1,
  },
  
  checkinTitle: {
    fontSize: typography.md,
    fontWeight: typography.medium,
    color: colors.text,
  },
  
  checkinSubtitle: {
    fontSize: typography.sm,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  
  sessionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: spacing.md,
    marginBottom: spacing.sm,
    ...commonStyles.card,
  },
  
  sessionIcon: {
    marginRight: spacing.md,
  },
  
  sessionInfo: {
    flex: 1,
  },
  
  sessionTitle: {
    fontSize: typography.md,
    fontWeight: typography.medium,
    color: colors.text,
  },
  
  sessionDetails: {
    fontSize: typography.sm,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  
  breathingGrid: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  
  breathingCard: {
    flex: 1,
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: spacing.md,
    alignItems: 'center',
    ...commonStyles.card,
  },
  
  breathingIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.sm,
  },
  
  breathingName: {
    fontSize: typography.sm,
    fontWeight: typography.medium,
    color: colors.text,
    textAlign: 'center',
  },
  
  breathingDuration: {
    fontSize: typography.xs,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  
  journalingCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: spacing.md,
    marginBottom: spacing.sm,
    ...commonStyles.card,
  },
  
  journalingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  
  journalingText: {
    marginLeft: spacing.md,
    flex: 1,
  },
  
  journalingTitle: {
    fontSize: typography.md,
    fontWeight: typography.medium,
    color: colors.text,
  },
  
  journalingSubtitle: {
    fontSize: typography.sm,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  
  statsContainer: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  
  statCard: {
    flex: 1,
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: spacing.md,
    alignItems: 'center',
    ...commonStyles.card,
  },
  
  statValue: {
    fontSize: typography.xl,
    fontWeight: typography.bold,
    color: colors.text,
  },
  
  statLabel: {
    fontSize: typography.sm,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: spacing.xs,
  },
});
