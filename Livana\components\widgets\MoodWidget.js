import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  Platform,
  Dimensions,
  KeyboardAvoidingView,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { spacing, typography, shadows } from '../../utils/theme';
import { useTheme } from '../../context/ThemeContext';
import { saveDailyData } from '../../data/storage';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const moodOptions = [
  { emoji: '😄', label: 'Excellent', value: 5, color: '#4CAF50' },
  { emoji: '😊', label: 'Good', value: 4, color: '#8BC34A' },
  { emoji: '😐', label: 'Okay', value: 3, color: '#FFC107' },
  { emoji: '😔', label: 'Low', value: 2, color: '#FF9800' },
  { emoji: '😢', label: 'Difficult', value: 1, color: '#F44336' },
];

export default function MoodWidget({ data, onUpdate }) {
  const { currentTheme: colors } = useTheme();
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedMood, setSelectedMood] = useState(null);
  const [moodNote, setMoodNote] = useState('');

  const currentMood = data?.mood;
  const currentMoodData = moodOptions.find(mood => mood.value === currentMood?.value);

  const handleMoodSelect = (mood) => {
    setSelectedMood(mood);
    setMoodNote(currentMood?.note || '');
  };

  const handleSaveMood = async () => {
    if (!selectedMood) return;

    const today = new Date().toISOString().split('T')[0];
    const moodData = {
      value: selectedMood.value,
      label: selectedMood.label,
      emoji: selectedMood.emoji,
      note: moodNote.trim(),
      timestamp: new Date().toISOString(),
    };

    try {
      await saveDailyData(today, { mood: moodData });
      setModalVisible(false);
      setSelectedMood(null);
      setMoodNote('');
      onUpdate && onUpdate();
    } catch (error) {
      console.error('Error saving mood:', error);
    }
  };

  const openMoodSelector = () => {
    setModalVisible(true);
  };

  return (
    <>
      <TouchableOpacity style={styles.container} onPress={openMoodSelector}>
        <View style={styles.header}>
          <View style={styles.iconContainer}>
            <Ionicons name="happy" size={24} color={colors.mood} />
          </View>
          <Text style={styles.title}>Mood</Text>
          <Ionicons name="chevron-forward" size={16} color={colors.textLight} />
        </View>

        <View style={styles.content}>
          {currentMoodData ? (
            <>
              <Text style={styles.moodEmoji}>{currentMoodData.emoji}</Text>
              <Text style={styles.moodLabel}>{currentMoodData.label}</Text>
              {currentMood?.note && (
                <Text style={styles.moodNote} numberOfLines={2}>
                  "{currentMood.note}"
                </Text>
              )}
            </>
          ) : (
            <>
              <Text style={styles.placeholder}>How are you feeling?</Text>
              <Text style={styles.placeholderSubtext}>Tap to log your mood</Text>
            </>
          )}
        </View>

        <View style={styles.footer}>
          <Text style={styles.timestamp}>
            {currentMood?.timestamp
              ? `Logged ${new Date(currentMood.timestamp).toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit',
                })}`
              : 'Not logged today'}
          </Text>
        </View>
      </TouchableOpacity>

      {/* Mood Selection Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <KeyboardAvoidingView
          style={styles.modalOverlay}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <SafeAreaView style={styles.modalContainer}>
            <View style={[styles.modalContent, { backgroundColor: colors.surface }]}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>How are you feeling?</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <Ionicons name="close" size={24} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>

            <View style={styles.moodOptions}>
              {moodOptions.map((mood) => (
                <TouchableOpacity
                  key={mood.value}
                  style={[
                    styles.moodOption,
                    selectedMood?.value === mood.value && styles.moodOptionSelected,
                  ]}
                  onPress={() => handleMoodSelect(mood)}
                >
                  <Text style={styles.moodOptionEmoji}>{mood.emoji}</Text>
                  <Text style={styles.moodOptionLabel}>{mood.label}</Text>
                </TouchableOpacity>
              ))}
            </View>

            {selectedMood && (
              <View style={styles.noteSection}>
                <Text style={styles.noteLabel}>Add a note (optional)</Text>
                <TextInput
                  style={styles.noteInput}
                  placeholder="What's on your mind?"
                  placeholderTextColor={colors.textLight}
                  value={moodNote}
                  onChangeText={setMoodNote}
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                />
              </View>
            )}

            <View style={styles.modalActions}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.saveButton,
                  !selectedMood && styles.saveButtonDisabled,
                ]}
                onPress={handleSaveMood}
                disabled={!selectedMood}
              >
                <Text style={[
                  styles.saveButtonText,
                  !selectedMood && styles.saveButtonTextDisabled,
                ]}>
                  Save Mood
                </Text>
              </TouchableOpacity>
            </View>
            </View>
          </SafeAreaView>
        </KeyboardAvoidingView>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.md,
    ...shadows.sm,
  },
  
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.mood + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  
  title: {
    flex: 1,
    fontSize: typography.md,
    fontWeight: typography.semibold,
    color: colors.text,
  },
  
  content: {
    alignItems: 'center',
    marginBottom: spacing.md,
    minHeight: 80,
    justifyContent: 'center',
  },
  
  moodEmoji: {
    fontSize: 48,
    marginBottom: spacing.sm,
  },
  
  moodLabel: {
    fontSize: typography.lg,
    fontWeight: typography.semibold,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  
  moodNote: {
    fontSize: typography.sm,
    color: colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  
  placeholder: {
    fontSize: typography.lg,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  
  placeholderSubtext: {
    fontSize: typography.sm,
    color: colors.textLight,
  },
  
  footer: {
    alignItems: 'center',
  },
  
  timestamp: {
    fontSize: typography.xs,
    color: colors.textLight,
  },
  
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },

  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },

  modalContent: {
    borderRadius: 20,
    padding: spacing.lg,
    width: '100%',
    maxWidth: screenWidth < 400 ? screenWidth - 40 : 400,
    maxHeight: screenHeight * 0.8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  
  modalTitle: {
    fontSize: typography.xl,
    fontWeight: typography.semibold,
    color: colors.text,
  },
  
  closeButton: {
    padding: spacing.xs,
  },
  
  moodOptions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: spacing.lg,
  },
  
  moodOption: {
    alignItems: 'center',
    padding: spacing.md,
    borderRadius: 12,
    minWidth: 70,
    minHeight: 80,
    justifyContent: 'center',
  },
  
  moodOptionSelected: {
    backgroundColor: colors.mood + '20',
  },
  
  moodOptionEmoji: {
    fontSize: 32,
    marginBottom: spacing.xs,
  },
  
  moodOptionLabel: {
    fontSize: typography.xs,
    color: colors.text,
    textAlign: 'center',
  },
  
  noteSection: {
    marginBottom: spacing.lg,
  },
  
  noteLabel: {
    fontSize: typography.sm,
    fontWeight: typography.medium,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  
  noteInput: {
    borderWidth: 1,
    borderColor: colors.surfaceLight,
    borderRadius: 8,
    padding: spacing.md,
    fontSize: typography.md,
    color: colors.text,
    minHeight: 80,
  },
  
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  
  cancelButton: {
    flex: 1,
    paddingVertical: spacing.md,
    alignItems: 'center',
    marginRight: spacing.sm,
  },
  
  cancelButtonText: {
    fontSize: typography.md,
    color: colors.textSecondary,
  },
  
  saveButton: {
    flex: 1,
    backgroundColor: colors.mood,
    paddingVertical: spacing.md,
    borderRadius: 8,
    alignItems: 'center',
    marginLeft: spacing.sm,
  },
  
  saveButtonDisabled: {
    backgroundColor: colors.surfaceLight,
  },
  
  saveButtonText: {
    fontSize: typography.md,
    fontWeight: typography.medium,
    color: colors.surface,
  },
  
  saveButtonTextDisabled: {
    color: colors.textLight,
  },
});
