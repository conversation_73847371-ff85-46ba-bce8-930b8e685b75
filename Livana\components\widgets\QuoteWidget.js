import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { spacing, typography, shadows } from '../../utils/theme';
import { useTheme } from '../../context/ThemeContext';

export default function QuoteWidget({ quote }) {
  const { currentTheme: colors } = useTheme();

  const handleShare = () => {
    // TODO: Implement share functionality
    console.log('Share quote:', quote);
  };

  return (
    <View style={[styles.container, {
      backgroundColor: colors.surface,
      borderLeftColor: colors.secondary,
      ...shadows.card
    }]}>
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          <Ionicons name="bulb" size={24} color={colors.secondary} />
        </View>
        <Text style={styles.title}>Daily Inspiration</Text>
        <TouchableOpacity style={styles.shareButton} onPress={handleShare}>
          <Ionicons name="share" size={16} color={colors.textLight} />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        <View style={styles.quoteContainer}>
          <Ionicons name="quote" size={20} color={colors.secondary} style={styles.quoteIcon} />
          <Text style={styles.quoteText}>{quote}</Text>
        </View>
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>✨ Let this inspire your day</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.md,
    ...shadows.sm,
    borderLeftWidth: 4,
    borderLeftColor: colors.secondary,
  },
  
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.secondary + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  
  title: {
    flex: 1,
    fontSize: typography.md,
    fontWeight: typography.semibold,
    color: colors.text,
  },
  
  shareButton: {
    padding: spacing.xs,
  },
  
  content: {
    marginBottom: spacing.md,
  },
  
  quoteContainer: {
    position: 'relative',
    paddingLeft: spacing.lg,
  },
  
  quoteIcon: {
    position: 'absolute',
    left: 0,
    top: 0,
  },
  
  quoteText: {
    fontSize: typography.md,
    color: colors.text,
    lineHeight: 22,
    fontStyle: 'italic',
  },
  
  footer: {
    alignItems: 'center',
  },
  
  footerText: {
    fontSize: typography.sm,
    color: colors.textSecondary,
  },
});
