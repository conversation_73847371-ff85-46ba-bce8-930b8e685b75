{"version": 3, "names": ["camelCaseToDashed", "camelCase", "replace", "m", "toLowerCase", "getBoundingClientRect", "node", "isElement", "nodeType", "Error", "measureLayout", "callback", "relativeNode", "parentNode", "setTimeout", "relativeRect", "height", "left", "top", "width", "x", "y", "remeasure", "tag", "state", "touchable", "responderID", "_handleQueryLayout", "encodeSvg", "svgString", "indexOf"], "sourceRoot": "../../../../src", "sources": ["web/utils/index.ts"], "mappings": "AAAA,OAAO,MAAMA,iBAAiB,GAAIC,SAAiB,IAAK;EACtD,OAAOA,SAAS,CAACC,OAAO,CAAC,QAAQ,EAAGC,CAAC,IAAK,GAAG,GAAGA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;AAClE,CAAC;AAED,OAAO,MAAMC,qBAAqB,GAAIC,IAAgB,IAAK;EACzD,IAAIA,IAAI,EAAE;IACR,MAAMC,SAAS,GAAGD,IAAI,CAACE,QAAQ,KAAK,CAAC,CAAC,CAAC;IACvC,IAAID,SAAS,IAAI,OAAOD,IAAI,CAACD,qBAAqB,KAAK,UAAU,EAAE;MACjE,OAAOC,IAAI,CAACD,qBAAqB,CAAC,CAAC;IACrC;EACF;EACA,MAAM,IAAII,KAAK,CAAC,oCAAoC,GAAGH,IAAI,IAAI,WAAW,CAAC;AAC7E,CAAC;AAED,MAAMI,aAAa,GAAGA,CACpBJ,IAAgB,EAChBK,QAOS,KACN;EACH,MAAMC,YAAY,GAAGN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,UAAU;EACrC,IAAID,YAAY,EAAE;IAChBE,UAAU,CAAC,MAAM;MACf;MACA,MAAMC,YAAY,GAAGV,qBAAqB,CAACO,YAAY,CAAC;MACxD,MAAM;QAAEI,MAAM;QAAEC,IAAI;QAAEC,GAAG;QAAEC;MAAM,CAAC,GAAGd,qBAAqB,CAACC,IAAI,CAAC;MAChE,MAAMc,CAAC,GAAGH,IAAI,GAAGF,YAAY,CAACE,IAAI;MAClC,MAAMI,CAAC,GAAGH,GAAG,GAAGH,YAAY,CAACG,GAAG;MAChCP,QAAQ,CAACS,CAAC,EAAEC,CAAC,EAAEF,KAAK,EAAEH,MAAM,EAAEC,IAAI,EAAEC,GAAG,CAAC;IAC1C,CAAC,EAAE,CAAC,CAAC;EACP;AACF,CAAC;;AAED;AACA,OAAO,SAASI,SAASA,CAAA,EAAY;EACnC,MAAMC,GAAG,GAAG,IAAI,CAACC,KAAK,CAACC,SAAS,CAACC,WAAW;EAC5C,IAAIH,GAAG,KAAK,IAAI,EAAE;IAChB;EACF;EACAb,aAAa,CAACa,GAAG,EAAE,IAAI,CAACI,kBAAkB,CAAC;AAC7C;;AAEA;AACA,OAAO,SAASC,SAASA,CAACC,SAAiB,EAAE;EAC3C,OAAOA,SAAS,CACb3B,OAAO,CACN,MAAM,EACN,CAAC2B,SAAS,CAACC,OAAO,CAAC,OAAO,CAAC,GACvB,MAAM,GACN,yCACN,CAAC,CACA5B,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAClBA,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CACpBA,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CACpBA,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CACpBA,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CACpBA,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CACpBA,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CACpBA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;AACzB", "ignoreList": []}