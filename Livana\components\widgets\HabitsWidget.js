import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography, shadows } from '../../utils/theme';

export default function HabitsWidget({ data, onUpdate, navigation }) {
  // Placeholder habits data
  const habits = [
    { id: 1, name: 'Meditate', completed: true, icon: 'leaf' },
    { id: 2, name: 'Exercise', completed: false, icon: 'fitness' },
    { id: 3, name: 'Read', completed: true, icon: 'book' },
  ];

  const completedCount = habits.filter(h => h.completed).length;
  const totalCount = habits.length;

  const handleHabitPress = () => {
    // Navigate to habits detail screen
    console.log('Navigate to habits screen');
  };

  return (
    <TouchableOpacity style={styles.container} onPress={handleHabitPress}>
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          <Ionicons name="checkmark-circle" size={24} color={colors.habits} />
        </View>
        <Text style={styles.title}>Daily Habits</Text>
        <Ionicons name="chevron-forward" size={16} color={colors.textLight} />
      </View>

      <View style={styles.content}>
        <View style={styles.progressContainer}>
          <Text style={styles.progressText}>
            {completedCount} of {totalCount} completed
          </Text>
          <View style={styles.progressBar}>
            <View style={[
              styles.progressFill,
              { width: `${(completedCount / totalCount) * 100}%` }
            ]} />
          </View>
        </View>

        <View style={styles.habitsList}>
          {habits.slice(0, 3).map((habit) => (
            <View key={habit.id} style={styles.habitItem}>
              <Ionicons
                name={habit.completed ? "checkmark-circle" : "ellipse-outline"}
                size={16}
                color={habit.completed ? colors.success : colors.textLight}
              />
              <Text style={[
                styles.habitName,
                habit.completed && styles.habitNameCompleted
              ]}>
                {habit.name}
              </Text>
            </View>
          ))}
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.md,
    ...shadows.sm,
  },
  
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.habits + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  
  title: {
    flex: 1,
    fontSize: typography.md,
    fontWeight: typography.semibold,
    color: colors.text,
  },
  
  content: {
    marginBottom: spacing.sm,
  },
  
  progressContainer: {
    marginBottom: spacing.md,
  },
  
  progressText: {
    fontSize: typography.sm,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  
  progressBar: {
    height: 6,
    backgroundColor: colors.surfaceLight,
    borderRadius: 3,
  },
  
  progressFill: {
    height: '100%',
    backgroundColor: colors.habits,
    borderRadius: 3,
  },
  
  habitsList: {
    gap: spacing.xs,
  },
  
  habitItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  habitName: {
    fontSize: typography.sm,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  
  habitNameCompleted: {
    textDecorationLine: 'line-through',
    color: colors.textSecondary,
  },
});
