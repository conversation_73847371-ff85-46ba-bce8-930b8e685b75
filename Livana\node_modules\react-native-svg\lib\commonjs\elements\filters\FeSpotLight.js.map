{"version": 3, "names": ["_react", "require", "_util", "FeSpotLight", "Component", "displayName", "defaultProps", "render", "warnUnimplementedFilter", "exports", "default"], "sourceRoot": "../../../../src", "sources": ["elements/filters/FeSpotLight.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAEA,IAAAC,KAAA,GAAAD,OAAA;AAae,MAAME,WAAW,SAASC,gBAAS,CAAmB;EACnE,OAAOC,WAAW,GAAG,aAAa;EAElC,OAAOC,YAAY,GAAG,CAAC,CAAC;EAExBC,MAAMA,CAAA,EAAG;IACP,IAAAC,6BAAuB,EAAC,CAAC;IACzB,OAAO,IAAI;EACb;AACF;AAACC,OAAA,CAAAC,OAAA,GAAAP,WAAA", "ignoreList": []}