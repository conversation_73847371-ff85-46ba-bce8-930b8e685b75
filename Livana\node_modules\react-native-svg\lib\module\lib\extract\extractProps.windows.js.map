{"version": 3, "names": ["extractFill", "extractStroke", "extractTransform", "extractResponder", "extractOpacity", "idPattern", "clipRules", "evenodd", "nonzero", "propsAndStyles", "props", "style", "Array", "isArray", "Object", "assign", "<PERSON><PERSON><PERSON><PERSON>", "marker", "undefined", "matched", "match", "extractProps", "ref", "id", "opacity", "onLayout", "clipPath", "clipRule", "display", "mask", "markerStart", "markerMid", "markerEnd", "extracted", "inherited", "color", "length", "propList", "matrix", "name", "String", "console", "warn", "extract", "instance", "withoutXY", "x", "y"], "sourceRoot": "../../../../src", "sources": ["lib/extract/extractProps.windows.ts"], "mappings": "AAAA,OAAOA,WAAW,MAAM,eAAe;AACvC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,SAAS,QAAQ,SAAS;AAYnC,MAAMC,SAA+C,GAAG;EACtDC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE;AACX,CAAC;AAED,OAAO,SAASC,cAAcA,CAACC,KAAwC,EAAE;EACvE,MAAM;IAAEC;EAAM,CAAC,GAAGD,KAAK;EACvB,OAAO,CAACC,KAAK,GACTD,KAAK,GACL;IACE,IAAIE,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,GAAGG,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAGJ,KAAK,CAAC,GAAGA,KAAK,CAAC;IAC/D,GAAGD;EACL,CAAC;AACP;AAEA,SAASM,SAASA,CAACC,MAAe,EAAE;EAClC,IAAI,CAACA,MAAM,EAAE;IACX,OAAOC,SAAS;EAClB;EACA,MAAMC,OAAO,GAAGF,MAAM,CAACG,KAAK,CAACf,SAAS,CAAC;EACvC,OAAOc,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC,GAAGD,SAAS;AACzC;AAEA,eAAe,SAASG,YAAYA,CAClCX,KAgBW,EACXY,GAAW,EACX;EACA,MAAM;IACJC,EAAE;IACFC,OAAO;IACPC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,OAAO;IACPC,IAAI;IACJZ,MAAM;IACNa,WAAW,GAAGb,MAAM;IACpBc,SAAS,GAAGd,MAAM;IAClBe,SAAS,GAAGf;EACd,CAAC,GAAGP,KAAK;EACT,MAAMuB,SAAyB,GAAG,CAAC,CAAC;EAEpC,MAAMC,SAAmB,GAAG,EAAE;EAC9B/B,gBAAgB,CAAC8B,SAAS,EAAEvB,KAAK,EAAEY,GAAG,CAAC;EACvCtB,WAAW,CAACiC,SAAS,EAAEvB,KAAK,EAAEwB,SAAS,CAAC;EACxCjC,aAAa,CAACgC,SAAS,EAAEvB,KAAK,EAAEwB,SAAS,CAAC;EAC1C,IAAIxB,KAAK,CAACyB,KAAK,EAAE;IACfF,SAAS,CAACE,KAAK,GAAGzB,KAAK,CAACyB,KAAK;EAC/B;EAEA,IAAID,SAAS,CAACE,MAAM,EAAE;IACpBH,SAAS,CAACI,QAAQ,GAAGH,SAAS;EAChC;EAEA,MAAMI,MAAM,GAAGpC,gBAAgB,CAACQ,KAAK,CAAC;EACtC,IAAI4B,MAAM,KAAK,IAAI,EAAE;IACnBL,SAAS,CAACK,MAAM,GAAGA,MAAM;EAC3B;EAEA,IAAId,OAAO,IAAI,IAAI,EAAE;IACnBS,SAAS,CAACT,OAAO,GAAGpB,cAAc,CAACoB,OAAO,CAAC;EAC7C;EAEA,IAAII,OAAO,IAAI,IAAI,EAAE;IACnBK,SAAS,CAACL,OAAO,GAAGA,OAAO,KAAK,MAAM,GAAG,MAAM,GAAGV,SAAS;EAC7D;EAEA,IAAIO,QAAQ,EAAE;IACZQ,SAAS,CAACR,QAAQ,GAAGA,QAAQ;EAC/B;EAEA,IAAIK,WAAW,EAAE;IACfG,SAAS,CAACH,WAAW,GAAGd,SAAS,CAACc,WAAW,CAAC;EAChD;EACA,IAAIC,SAAS,EAAE;IACbE,SAAS,CAACF,SAAS,GAAGf,SAAS,CAACe,SAAS,CAAC;EAC5C;EACA,IAAIC,SAAS,EAAE;IACbC,SAAS,CAACD,SAAS,GAAGhB,SAAS,CAACgB,SAAS,CAAC;EAC5C;EAEA,IAAIT,EAAE,EAAE;IACNU,SAAS,CAACM,IAAI,GAAGC,MAAM,CAACjB,EAAE,CAAC;EAC7B;EAEA,IAAII,QAAQ,EAAE;IACZM,SAAS,CAACN,QAAQ,GAAGrB,SAAS,CAACqB,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;EACxD;EACA,IAAID,QAAQ,EAAE;IACZ,MAAMP,OAAO,GAAGO,QAAQ,CAACN,KAAK,CAACf,SAAS,CAAC;IACzC,IAAIc,OAAO,EAAE;MACXc,SAAS,CAACP,QAAQ,GAAGP,OAAO,CAAC,CAAC,CAAC;IACjC,CAAC,MAAM;MACLsB,OAAO,CAACC,IAAI,CACV,qEAAqE,GACnEhB,QAAQ,GACR,GACJ,CAAC;IACH;EACF;EAEA,IAAIG,IAAI,EAAE;IACR,MAAMV,OAAO,GAAGU,IAAI,CAACT,KAAK,CAACf,SAAS,CAAC;IAErC,IAAIc,OAAO,EAAE;MACXc,SAAS,CAACJ,IAAI,GAAGV,OAAO,CAAC,CAAC,CAAC;IAC7B,CAAC,MAAM;MACLsB,OAAO,CAACC,IAAI,CACV,6DAA6D,GAC3Db,IAAI,GACJ,GACJ,CAAC;IACH;EACF;EAEA,OAAOI,SAAS;AAClB;AAEA,OAAO,SAASU,OAAOA,CACrBC,QAAgB,EAChBlC,KAAwC,EACxC;EACA,OAAOW,YAAY,CAACZ,cAAc,CAACC,KAAK,CAAC,EAAEkC,QAAQ,CAAC;AACtD;AAEA,OAAO,SAASC,SAASA,CACvBD,QAAgB,EAChBlC,KAAwC,EACxC;EACA,OAAOW,YAAY,CAAC;IAAE,GAAGZ,cAAc,CAACC,KAAK,CAAC;IAAEoC,CAAC,EAAE,IAAI;IAAEC,CAAC,EAAE;EAAK,CAAC,EAAEH,QAAQ,CAAC;AAC/E", "ignoreList": []}