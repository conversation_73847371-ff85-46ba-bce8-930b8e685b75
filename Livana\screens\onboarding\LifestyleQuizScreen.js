import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography, commonStyles } from '../../utils/theme';
import { lifestyleQuizQuestions } from '../../data/sampleData';

export default function LifestyleQuizScreen({ navigation }) {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [selectedOptions, setSelectedOptions] = useState([]);

  const currentQuestion = lifestyleQuizQuestions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === lifestyleQuizQuestions.length - 1;
  const progress = ((currentQuestionIndex + 1) / lifestyleQuizQuestions.length) * 100;

  const handleOptionSelect = (value) => {
    if (currentQuestion.type === 'multiple') {
      const newSelection = selectedOptions.includes(value)
        ? selectedOptions.filter(option => option !== value)
        : [...selectedOptions, value];
      setSelectedOptions(newSelection);
    } else {
      setSelectedOptions([value]);
    }
  };

  const handleNext = () => {
    // Save current answer
    const answer = currentQuestion.type === 'multiple' ? selectedOptions : selectedOptions[0];
    setAnswers(prev => ({
      ...prev,
      [currentQuestion.id]: answer
    }));

    if (isLastQuestion) {
      // Navigate to goal setting with quiz results
      navigation.navigate('GoalSetting', { quizAnswers: { ...answers, [currentQuestion.id]: answer } });
    } else {
      // Move to next question
      setCurrentQuestionIndex(prev => prev + 1);
      setSelectedOptions([]);
    }
  };

  const handleBack = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
      // Restore previous answer
      const prevAnswer = answers[lifestyleQuizQuestions[currentQuestionIndex - 1].id];
      setSelectedOptions(Array.isArray(prevAnswer) ? prevAnswer : [prevAnswer]);
    } else {
      navigation.goBack();
    }
  };

  const canProceed = selectedOptions.length > 0;

  const renderScaleQuestion = () => {
    return (
      <View style={styles.scaleContainer}>
        <View style={styles.scaleLabels}>
          <Text style={styles.scaleLabel}>{currentQuestion.labels[currentQuestion.min]}</Text>
          <Text style={styles.scaleLabel}>{currentQuestion.labels[currentQuestion.max]}</Text>
        </View>
        <View style={styles.scaleButtons}>
          {Array.from({ length: currentQuestion.max - currentQuestion.min + 1 }, (_, i) => {
            const value = currentQuestion.min + i;
            const isSelected = selectedOptions.includes(value);
            return (
              <TouchableOpacity
                key={value}
                style={[styles.scaleButton, isSelected && styles.scaleButtonSelected]}
                onPress={() => handleOptionSelect(value)}
              >
                <Text style={[styles.scaleButtonText, isSelected && styles.scaleButtonTextSelected]}>
                  {value}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  };

  const renderMultipleChoiceQuestion = () => {
    return (
      <View style={styles.optionsContainer}>
        {currentQuestion.options.map((option) => {
          const isSelected = selectedOptions.includes(option.value);
          return (
            <TouchableOpacity
              key={option.value}
              style={[styles.optionButton, isSelected && styles.optionButtonSelected]}
              onPress={() => handleOptionSelect(option.value)}
            >
              <View style={styles.optionContent}>
                <Text style={[styles.optionText, isSelected && styles.optionTextSelected]}>
                  {option.label}
                </Text>
                {isSelected && (
                  <Ionicons name="checkmark-circle" size={24} color={colors.primary} />
                )}
              </View>
            </TouchableOpacity>
          );
        })}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Progress bar */}
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View style={[styles.progressFill, { width: `${progress}%` }]} />
        </View>
        <Text style={styles.progressText}>
          {currentQuestionIndex + 1} of {lifestyleQuizQuestions.length}
        </Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Question */}
        <View style={styles.questionContainer}>
          <Text style={styles.questionText}>{currentQuestion.question}</Text>
        </View>

        {/* Answer options */}
        {currentQuestion.type === 'scale' ? renderScaleQuestion() : renderMultipleChoiceQuestion()}
      </ScrollView>

      {/* Navigation buttons */}
      <View style={styles.navigationContainer}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Ionicons name="arrow-back" size={20} color={colors.textSecondary} />
          <Text style={styles.backButtonText}>Back</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.nextButton, !canProceed && styles.nextButtonDisabled]}
          onPress={handleNext}
          disabled={!canProceed}
        >
          <Text style={[styles.nextButtonText, !canProceed && styles.nextButtonTextDisabled]}>
            {isLastQuestion ? 'Complete' : 'Next'}
          </Text>
          <Ionicons 
            name={isLastQuestion ? "checkmark" : "arrow-forward"} 
            size={20} 
            color={canProceed ? colors.surface : colors.textLight} 
          />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  
  progressContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
  },
  
  progressBar: {
    height: 4,
    backgroundColor: colors.surfaceLight,
    borderRadius: 2,
    marginBottom: spacing.sm,
  },
  
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 2,
  },
  
  progressText: {
    fontSize: typography.sm,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  
  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  
  questionContainer: {
    paddingVertical: spacing.xl,
  },
  
  questionText: {
    fontSize: typography.xl,
    fontWeight: typography.semibold,
    color: colors.text,
    lineHeight: 28,
    textAlign: 'center',
  },
  
  optionsContainer: {
    paddingBottom: spacing.xl,
  },
  
  optionButton: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderWidth: 2,
    borderColor: colors.surfaceLight,
  },
  
  optionButtonSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.primaryLight + '20',
  },
  
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  
  optionText: {
    fontSize: typography.md,
    color: colors.text,
    flex: 1,
  },
  
  optionTextSelected: {
    color: colors.primary,
    fontWeight: typography.medium,
  },
  
  scaleContainer: {
    paddingVertical: spacing.xl,
  },
  
  scaleLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.lg,
  },
  
  scaleLabel: {
    fontSize: typography.sm,
    color: colors.textSecondary,
    fontWeight: typography.medium,
  },
  
  scaleButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.sm,
  },
  
  scaleButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    borderWidth: 2,
    borderColor: colors.surfaceLight,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  scaleButtonSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  
  scaleButtonText: {
    fontSize: typography.md,
    color: colors.text,
    fontWeight: typography.medium,
  },
  
  scaleButtonTextSelected: {
    color: colors.surface,
  },
  
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    borderTopWidth: 1,
    borderTopColor: colors.surfaceLight,
  },
  
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
  },
  
  backButtonText: {
    fontSize: typography.md,
    color: colors.textSecondary,
    marginLeft: spacing.sm,
  },
  
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.lg,
    borderRadius: 20,
  },
  
  nextButtonDisabled: {
    backgroundColor: colors.surfaceLight,
  },
  
  nextButtonText: {
    fontSize: typography.md,
    fontWeight: typography.medium,
    color: colors.surface,
    marginRight: spacing.sm,
  },
  
  nextButtonTextDisabled: {
    color: colors.textLight,
  },
});
