import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { Platform } from 'react-native';
import AppNavigator from './navigation/AppNavigator';
import { AppProvider } from './context/AppContext';
import { ThemeProvider } from './context/ThemeContext';

export default function App() {
  return (
    <ThemeProvider>
      <AppProvider>
        <StatusBar
          style="auto"
          backgroundColor="transparent"
          translucent={Platform.OS === 'android'}
        />
        <AppNavigator />
      </AppProvider>
    </ThemeProvider>
  );
}
