import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography, shadows } from '../../utils/theme';

export default function AgendaWidget({ data, onUpdate, navigation }) {
  const todayTasks = [
    { id: 1, title: 'Morning meditation', completed: true, time: '7:00 AM' },
    { id: 2, title: 'Workout session', completed: false, time: '6:00 PM' },
    { id: 3, title: 'Evening walk', completed: false, time: '8:00 PM' },
  ];

  const completedTasks = todayTasks.filter(task => task.completed).length;
  
  return (
    <TouchableOpacity style={styles.container}>
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          <Ionicons name="calendar-today" size={24} color={colors.primary} />
        </View>
        <Text style={styles.title}>Today's Agenda</Text>
        <Ionicons name="chevron-forward" size={16} color={colors.textLight} />
      </View>

      <View style={styles.content}>
        <Text style={styles.summary}>
          {completedTasks} of {todayTasks.length} tasks completed
        </Text>
        
        <View style={styles.tasksList}>
          {todayTasks.slice(0, 2).map((task) => (
            <View key={task.id} style={styles.taskItem}>
              <Ionicons
                name={task.completed ? "checkmark-circle" : "ellipse-outline"}
                size={16}
                color={task.completed ? colors.success : colors.textLight}
              />
              <View style={styles.taskContent}>
                <Text style={[
                  styles.taskTitle,
                  task.completed && styles.taskTitleCompleted
                ]}>
                  {task.title}
                </Text>
                <Text style={styles.taskTime}>{task.time}</Text>
              </View>
            </View>
          ))}
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.md,
    ...shadows.sm,
  },
  
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.primary + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  
  title: {
    flex: 1,
    fontSize: typography.md,
    fontWeight: typography.semibold,
    color: colors.text,
  },
  
  content: {
    marginBottom: spacing.sm,
  },
  
  summary: {
    fontSize: typography.sm,
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  
  tasksList: {
    gap: spacing.sm,
  },
  
  taskItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  taskContent: {
    flex: 1,
    marginLeft: spacing.sm,
  },
  
  taskTitle: {
    fontSize: typography.sm,
    color: colors.text,
  },
  
  taskTitleCompleted: {
    textDecorationLine: 'line-through',
    color: colors.textSecondary,
  },
  
  taskTime: {
    fontSize: typography.xs,
    color: colors.textLight,
  },
});
