import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Import all themes
import { getTheme, themeNames } from '../utils/theme';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

const THEME_STORAGE_KEY = 'livana_selected_theme';

export const ThemeProvider = ({ children }) => {
  const [currentThemeName, setCurrentThemeName] = useState('sage');
  const [currentTheme, setCurrentTheme] = useState(getTheme('sage'));
  const [isLoading, setIsLoading] = useState(true);

  // Load saved theme on app start
  useEffect(() => {
    loadSavedTheme();
  }, []);

  const loadSavedTheme = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY);
      if (savedTheme && themeNames.includes(savedTheme)) {
        setCurrentThemeName(savedTheme);
        setCurrentTheme(getTheme(savedTheme));
      }
    } catch (error) {
      console.error('Error loading saved theme:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const changeTheme = async (themeName) => {
    if (!themeNames.includes(themeName)) {
      console.warn(`Theme "${themeName}" not found. Available themes:`, themeNames);
      return;
    }

    try {
      setCurrentThemeName(themeName);
      setCurrentTheme(getTheme(themeName));
      await AsyncStorage.setItem(THEME_STORAGE_KEY, themeName);
    } catch (error) {
      console.error('Error saving theme:', error);
    }
  };

  const value = {
    currentThemeName,
    currentTheme,
    availableThemes: themeNames,
    changeTheme,
    isLoading,
    
    // Theme-specific utilities
    isDarkTheme: currentThemeName === 'dark',
    isLightTheme: currentThemeName === 'light',
    isPinkTheme: currentThemeName === 'pink',
    isNavyTheme: currentThemeName === 'navy',
    isSageTheme: currentThemeName === 'sage',
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
