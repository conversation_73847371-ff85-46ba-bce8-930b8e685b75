import { Platform, Dimensions } from 'react-native';

// Get device dimensions
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Device type detection
export const deviceInfo = {
  isIOS: Platform.OS === 'ios',
  isAndroid: Platform.OS === 'android',
  isWeb: Platform.OS === 'web',
  screenWidth,
  screenHeight,
  isSmallScreen: screenWidth < 375,
  isMediumScreen: screenWidth >= 375 && screenWidth < 414,
  isLargeScreen: screenWidth >= 414,
  isTablet: screenWidth >= 768,
};

// Performance optimizations
export const performanceConfig = {
  // Reduce animations on slower devices
  shouldReduceAnimations: deviceInfo.isAndroid && screenWidth < 375,
  
  // Optimize image loading
  imageQuality: deviceInfo.isSmallScreen ? 0.7 : 0.9,
  
  // Debounce delays
  debounceDelay: deviceInfo.isAndroid ? 300 : 200,
  
  // Maximum concurrent operations
  maxConcurrentOperations: deviceInfo.isSmallScreen ? 2 : 4,
};

// Touch target optimization
export const touchTargets = {
  minimum: 44, // iOS HIG and Android Material Design minimum
  recommended: 48,
  comfortable: 56,
};

// Responsive spacing
export const responsiveSpacing = {
  xs: deviceInfo.isSmallScreen ? 2 : 4,
  sm: deviceInfo.isSmallScreen ? 6 : 8,
  md: deviceInfo.isSmallScreen ? 12 : 16,
  lg: deviceInfo.isSmallScreen ? 18 : 24,
  xl: deviceInfo.isSmallScreen ? 24 : 32,
  xxl: deviceInfo.isSmallScreen ? 36 : 48,
};

// Responsive typography
export const responsiveTypography = {
  xs: deviceInfo.isSmallScreen ? 10 : 11,
  sm: deviceInfo.isSmallScreen ? 12 : 13,
  md: deviceInfo.isSmallScreen ? 14 : 15,
  lg: deviceInfo.isSmallScreen ? 16 : 17,
  xl: deviceInfo.isSmallScreen ? 18 : 19,
  xxl: deviceInfo.isSmallScreen ? 20 : 22,
  xxxl: deviceInfo.isSmallScreen ? 24 : 28,
  display: deviceInfo.isSmallScreen ? 28 : 34,
};

// Modal sizing for different screen sizes
export const modalSizing = {
  width: deviceInfo.isTablet ? '60%' : '90%',
  maxWidth: deviceInfo.isTablet ? 600 : 400,
  maxHeight: deviceInfo.isSmallScreen ? '85%' : '80%',
  padding: deviceInfo.isSmallScreen ? 16 : 20,
};

// Safe area handling
export const safeAreaConfig = {
  paddingTop: deviceInfo.isIOS ? 44 : 24,
  paddingBottom: deviceInfo.isIOS ? 34 : 0,
  statusBarHeight: deviceInfo.isIOS ? 44 : 24,
};

// Animation configurations
export const animationConfig = {
  duration: performanceConfig.shouldReduceAnimations ? 200 : 300,
  easing: 'ease-out',
  useNativeDriver: true,
};

// Debounce utility for performance
export const debounce = (func, delay = performanceConfig.debounceDelay) => {
  let timeoutId;
  return (...args) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(null, args), delay);
  };
};

// Throttle utility for scroll events
export const throttle = (func, limit = 100) => {
  let inThrottle;
  return (...args) => {
    if (!inThrottle) {
      func.apply(null, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// Memory management utilities
export const memoryOptimization = {
  // Clear image cache periodically
  clearImageCache: () => {
    if (deviceInfo.isAndroid) {
      // Android-specific image cache clearing
      console.log('Clearing image cache for Android');
    }
  },
  
  // Optimize component rendering
  shouldComponentUpdate: (prevProps, nextProps, keys) => {
    return keys.some(key => prevProps[key] !== nextProps[key]);
  },
  
  // Lazy loading configuration
  lazyLoadOffset: deviceInfo.isSmallScreen ? 100 : 200,
};

// Network optimization
export const networkConfig = {
  timeout: deviceInfo.isAndroid ? 10000 : 8000,
  retryAttempts: 3,
  retryDelay: 1000,
};

// Accessibility helpers
export const accessibilityConfig = {
  minimumTouchTarget: touchTargets.minimum,
  focusable: true,
  accessible: true,
  accessibilityRole: 'button',
};

// Platform-specific styles helper
export const platformStyles = (iosStyle, androidStyle, webStyle = {}) => {
  if (deviceInfo.isIOS) return iosStyle;
  if (deviceInfo.isAndroid) return androidStyle;
  return webStyle;
};

// Responsive design helper
export const responsiveStyle = (smallScreen, mediumScreen, largeScreen) => {
  if (deviceInfo.isSmallScreen) return smallScreen;
  if (deviceInfo.isMediumScreen) return mediumScreen;
  return largeScreen;
};

// Error boundary helper for mobile
export const handleMobileError = (error, errorInfo) => {
  console.error('Mobile Error:', error);
  console.error('Error Info:', errorInfo);
  
  // Log to crash analytics service
  if (deviceInfo.isIOS || deviceInfo.isAndroid) {
    // Crashlytics or similar service
    console.log('Logging to mobile crash analytics');
  }
};

// Performance monitoring
export const performanceMonitor = {
  startTime: null,
  
  start: (label) => {
    performanceMonitor.startTime = Date.now();
    console.log(`Performance: ${label} started`);
  },
  
  end: (label) => {
    if (performanceMonitor.startTime) {
      const duration = Date.now() - performanceMonitor.startTime;
      console.log(`Performance: ${label} completed in ${duration}ms`);
      performanceMonitor.startTime = null;
    }
  },
};

export default {
  deviceInfo,
  performanceConfig,
  touchTargets,
  responsiveSpacing,
  responsiveTypography,
  modalSizing,
  safeAreaConfig,
  animationConfig,
  debounce,
  throttle,
  memoryOptimization,
  networkConfig,
  accessibilityConfig,
  platformStyles,
  responsiveStyle,
  handleMobileError,
  performanceMonitor,
};
