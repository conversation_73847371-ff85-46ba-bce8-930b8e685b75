import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Alert,
  Platform,
  Dimensions,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { spacing, typography, shadows } from '../../utils/theme';
import { useTheme } from '../../context/ThemeContext';
import { saveDailyData } from '../../data/storage';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export default function SleepWidget({ data, onUpdate }) {
  const { currentTheme: colors } = useTheme();
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedHours, setSelectedHours] = useState(8);

  const sleepHours = data?.sleep || 0;
  const target = 8;
  const quality = sleepHours >= target ? 'Good' : sleepHours >= 6 ? 'Fair' : 'Poor';
  const qualityColor = sleepHours >= target ? colors.success : sleepHours >= 6 ? colors.warning : colors.error;

  const handleSaveSleep = async () => {
    const today = new Date().toISOString().split('T')[0];
    
    try {
      await saveDailyData(today, { sleep: selectedHours });
      setModalVisible(false);
      onUpdate && onUpdate();
    } catch (error) {
      console.error('Error saving sleep data:', error);
      Alert.alert('Error', 'Failed to save sleep data');
    }
  };

  const openSleepLogger = () => {
    setSelectedHours(sleepHours || 8);
    setModalVisible(true);
  };

  const renderHourSelector = () => {
    const hours = [];
    for (let i = 1; i <= 12; i++) {
      hours.push(
        <TouchableOpacity
          key={i}
          style={[
            styles.hourButton,
            selectedHours === i && styles.hourButtonSelected,
          ]}
          onPress={() => setSelectedHours(i)}
        >
          <Text style={[
            styles.hourButtonText,
            selectedHours === i && styles.hourButtonTextSelected,
          ]}>
            {i}
          </Text>
        </TouchableOpacity>
      );
    }
    return hours;
  };

  return (
    <>
      <TouchableOpacity style={styles.container} onPress={openSleepLogger}>
        <View style={styles.header}>
          <View style={styles.iconContainer}>
            <Ionicons name="moon" size={24} color={colors.sleep} />
          </View>
          <Text style={styles.title}>Sleep</Text>
          <Ionicons name="chevron-forward" size={16} color={colors.textLight} />
        </View>

        <View style={styles.content}>
          {sleepHours > 0 ? (
            <>
              <View style={styles.sleepInfo}>
                <Text style={styles.hoursText}>{sleepHours}h</Text>
                <Text style={styles.targetText}>of {target}h</Text>
              </View>
              
              <View style={styles.qualityContainer}>
                <View style={[styles.qualityIndicator, { backgroundColor: qualityColor }]} />
                <Text style={[styles.qualityText, { color: qualityColor }]}>
                  {quality} sleep
                </Text>
              </View>

              <View style={styles.progressContainer}>
                <View style={styles.progressBar}>
                  <View style={[
                    styles.progressFill,
                    { 
                      width: `${Math.min((sleepHours / target) * 100, 100)}%`,
                      backgroundColor: qualityColor,
                    }
                  ]} />
                </View>
              </View>
            </>
          ) : (
            <View style={styles.placeholder}>
              <Ionicons name="bed" size={32} color={colors.textLight} />
              <Text style={styles.placeholderText}>Log your sleep</Text>
              <Text style={styles.placeholderSubtext}>Tap to record hours slept</Text>
            </View>
          )}
        </View>

        <View style={styles.footer}>
          <Text style={styles.tip}>
            {sleepHours >= target
              ? '😴 Great rest! You\'re well-rested'
              : sleepHours >= 6
              ? '🌙 Not bad, but aim for 8 hours'
              : sleepHours > 0
              ? '⏰ Try to get more sleep tonight'
              : '💤 Track your sleep for better insights'}
          </Text>
        </View>
      </TouchableOpacity>

      {/* Sleep Logger Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <SafeAreaView style={styles.modalContainer}>
            <View style={[styles.modalContent, { backgroundColor: colors.surface }]}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>How many hours did you sleep?</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <Ionicons name="close" size={24} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>

            <View style={styles.selectedHoursContainer}>
              <Text style={styles.selectedHoursText}>{selectedHours}</Text>
              <Text style={styles.selectedHoursLabel}>hours</Text>
            </View>

            <View style={styles.hoursGrid}>
              {renderHourSelector()}
            </View>

            <View style={styles.modalActions}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.saveButton}
                onPress={handleSaveSleep}
              >
                <Text style={styles.saveButtonText}>Save Sleep</Text>
              </TouchableOpacity>
            </View>
            </View>
          </SafeAreaView>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.md,
    ...shadows.sm,
  },
  
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.sleep + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  
  title: {
    flex: 1,
    fontSize: typography.md,
    fontWeight: typography.semibold,
    color: colors.text,
  },
  
  content: {
    alignItems: 'center',
    marginBottom: spacing.md,
    minHeight: 80,
    justifyContent: 'center',
  },
  
  sleepInfo: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: spacing.sm,
  },
  
  hoursText: {
    fontSize: typography.xxxl,
    fontWeight: typography.bold,
    color: colors.sleep,
  },
  
  targetText: {
    fontSize: typography.md,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
  },
  
  qualityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  
  qualityIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: spacing.xs,
  },
  
  qualityText: {
    fontSize: typography.sm,
    fontWeight: typography.medium,
  },
  
  progressContainer: {
    width: '100%',
  },
  
  progressBar: {
    width: '100%',
    height: 6,
    backgroundColor: colors.surfaceLight,
    borderRadius: 3,
  },
  
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  
  placeholder: {
    alignItems: 'center',
  },
  
  placeholderText: {
    fontSize: typography.md,
    color: colors.textSecondary,
    marginTop: spacing.sm,
  },
  
  placeholderSubtext: {
    fontSize: typography.sm,
    color: colors.textLight,
    marginTop: spacing.xs,
  },
  
  footer: {
    alignItems: 'center',
  },
  
  tip: {
    fontSize: typography.sm,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },

  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },

  modalContent: {
    borderRadius: 20,
    padding: spacing.lg,
    width: '100%',
    maxWidth: screenWidth < 400 ? screenWidth - 40 : 400,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  
  modalTitle: {
    fontSize: typography.lg,
    fontWeight: typography.semibold,
    color: colors.text,
    flex: 1,
  },
  
  closeButton: {
    padding: spacing.xs,
  },
  
  selectedHoursContainer: {
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  
  selectedHoursText: {
    fontSize: 48,
    fontWeight: typography.bold,
    color: colors.sleep,
  },
  
  selectedHoursLabel: {
    fontSize: typography.md,
    color: colors.textSecondary,
  },
  
  hoursGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginBottom: spacing.lg,
  },
  
  hourButton: {
    width: 54,
    height: 54,
    borderRadius: 27,
    alignItems: 'center',
    justifyContent: 'center',
    margin: spacing.xs,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  
  hourButtonSelected: {
    backgroundColor: colors.sleep,
  },
  
  hourButtonText: {
    fontSize: typography.md,
    color: colors.text,
    fontWeight: typography.medium,
  },
  
  hourButtonTextSelected: {
    color: colors.surface,
  },
  
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  
  cancelButton: {
    flex: 1,
    paddingVertical: spacing.md,
    alignItems: 'center',
    marginRight: spacing.sm,
  },
  
  cancelButtonText: {
    fontSize: typography.md,
    color: colors.textSecondary,
  },
  
  saveButton: {
    flex: 1,
    backgroundColor: colors.sleep,
    paddingVertical: spacing.md,
    borderRadius: 8,
    alignItems: 'center',
    marginLeft: spacing.sm,
  },
  
  saveButtonText: {
    fontSize: typography.md,
    fontWeight: typography.medium,
    color: colors.surface,
  },
});
