import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  Image,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography, commonStyles } from '../../utils/theme';

export default function WelcomeScreen({ navigation }) {
  const handleGetStarted = () => {
    navigation.navigate('LifestyleQuiz');
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" backgroundColor={colors.primary} />
      
      {/* Header with logo/icon */}
      <View style={styles.header}>
        <View style={styles.logoContainer}>
          <Ionicons name="leaf" size={60} color={colors.surface} />
        </View>
        <Text style={styles.appName}>Livana</Text>
        <Text style={styles.tagline}>Your Holistic Wellness Companion</Text>
      </View>

      {/* Main content */}
      <View style={styles.content}>
        <View style={styles.featureList}>
          <View style={styles.featureItem}>
            <Ionicons name="fitness" size={24} color={colors.secondary} />
            <Text style={styles.featureText}>Track your fitness journey</Text>
          </View>
          
          <View style={styles.featureItem}>
            <Ionicons name="restaurant" size={24} color={colors.secondary} />
            <Text style={styles.featureText}>Discover healthy recipes</Text>
          </View>
          
          <View style={styles.featureItem}>
            <Ionicons name="leaf" size={24} color={colors.secondary} />
            <Text style={styles.featureText}>Practice mindfulness daily</Text>
          </View>
          
          <View style={styles.featureItem}>
            <Ionicons name="calendar" size={24} color={colors.secondary} />
            <Text style={styles.featureText}>Plan your wellness agenda</Text>
          </View>
          
          <View style={styles.featureItem}>
            <Ionicons name="heart" size={24} color={colors.secondary} />
            <Text style={styles.featureText}>Monitor your mood & habits</Text>
          </View>
        </View>

        <View style={styles.welcomeMessage}>
          <Text style={styles.welcomeTitle}>Welcome to Your Wellness Journey</Text>
          <Text style={styles.welcomeDescription}>
            Livana helps you create a balanced, mindful lifestyle through personalized 
            tracking, guided activities, and gentle reminders to care for yourself.
          </Text>
        </View>
      </View>

      {/* Footer with get started button */}
      <View style={styles.footer}>
        <TouchableOpacity style={styles.getStartedButton} onPress={handleGetStarted}>
          <Text style={styles.getStartedText}>Get Started</Text>
          <Ionicons name="arrow-forward" size={20} color={colors.surface} />
        </TouchableOpacity>
        
        <Text style={styles.footerText}>
          Let's personalize your experience with a quick lifestyle quiz
        </Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.primary,
  },
  
  header: {
    flex: 0.4,
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: spacing.xl,
  },
  
  logoContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: colors.primaryDark,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.lg,
  },
  
  appName: {
    fontSize: typography.xxxl,
    fontWeight: typography.bold,
    color: colors.surface,
    marginBottom: spacing.sm,
  },
  
  tagline: {
    fontSize: typography.lg,
    color: colors.surface,
    opacity: 0.9,
    textAlign: 'center',
  },
  
  content: {
    flex: 0.5,
    backgroundColor: colors.surface,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl,
  },
  
  featureList: {
    marginBottom: spacing.xl,
  },
  
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    paddingVertical: spacing.sm,
  },
  
  featureText: {
    fontSize: typography.md,
    color: colors.text,
    marginLeft: spacing.md,
    flex: 1,
  },
  
  welcomeMessage: {
    alignItems: 'center',
  },
  
  welcomeTitle: {
    fontSize: typography.xl,
    fontWeight: typography.semibold,
    color: colors.text,
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  
  welcomeDescription: {
    fontSize: typography.md,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  
  footer: {
    flex: 0.1,
    backgroundColor: colors.surface,
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.lg,
    alignItems: 'center',
  },
  
  getStartedButton: {
    backgroundColor: colors.secondary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.xl,
    borderRadius: 25,
    marginBottom: spacing.md,
    minWidth: 200,
  },
  
  getStartedText: {
    fontSize: typography.lg,
    fontWeight: typography.semibold,
    color: colors.surface,
    marginRight: spacing.sm,
  },
  
  footerText: {
    fontSize: typography.sm,
    color: colors.textLight,
    textAlign: 'center',
  },
});
