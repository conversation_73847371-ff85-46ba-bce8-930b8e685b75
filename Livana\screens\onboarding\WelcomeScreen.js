import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Dimensions,
  Platform,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import { spacing, typography } from '../../utils/theme';
import { useTheme } from '../../context/ThemeContext';

const { width, height } = Dimensions.get('window');

export default function WelcomeScreen({ navigation }) {
  const { currentTheme: colors } = useTheme();

  const handleGetStarted = () => {
    navigation.navigate('LifestyleQuiz');
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.primary }]}>
      <StatusBar style="light" backgroundColor={colors.primary} />

      {/* Hero Header */}
      <View style={[styles.header, { backgroundColor: colors.primary }]}>
        <View style={[styles.logoContainer, {
          backgroundColor: colors.primaryDark,
          shadowColor: colors.primaryDark,
        }]}>
          <Ionicons name="leaf" size={64} color={colors.surface} />
        </View>
        <Text style={[styles.appName, { color: colors.surface }]}>Livana</Text>
        <Text style={[styles.tagline, { color: colors.surface }]}>Your Holistic Wellness Companion</Text>
      </View>

      {/* Main Content with Beautiful Card Design */}
      <View style={[styles.content, { backgroundColor: colors.surface }]}>
        <View style={styles.featureList}>
          <View style={[styles.featureItem, { backgroundColor: colors.background }]}>
            <View style={[styles.featureIcon, { backgroundColor: colors.steps + '20' }]}>
              <Ionicons name="fitness" size={26} color={colors.steps} />
            </View>
            <View style={styles.featureTextContainer}>
              <Text style={[styles.featureTitle, { color: colors.text }]}>Fitness Tracking</Text>
              <Text style={[styles.featureText, { color: colors.textSecondary }]}>Monitor your daily activities</Text>
            </View>
          </View>

          <View style={[styles.featureItem, { backgroundColor: colors.background }]}>
            <View style={[styles.featureIcon, { backgroundColor: colors.mood + '20' }]}>
              <Ionicons name="restaurant" size={26} color={colors.mood} />
            </View>
            <View style={styles.featureTextContainer}>
              <Text style={[styles.featureTitle, { color: colors.text }]}>Nutrition</Text>
              <Text style={[styles.featureText, { color: colors.textSecondary }]}>Discover healthy recipes</Text>
            </View>
          </View>

          <View style={[styles.featureItem, { backgroundColor: colors.background }]}>
            <View style={[styles.featureIcon, { backgroundColor: colors.water + '20' }]}>
              <Ionicons name="leaf" size={26} color={colors.water} />
            </View>
            <View style={styles.featureTextContainer}>
              <Text style={[styles.featureTitle, { color: colors.text }]}>Mindfulness</Text>
              <Text style={[styles.featureText, { color: colors.textSecondary }]}>Practice daily meditation</Text>
            </View>
          </View>

          <View style={[styles.featureItem, { backgroundColor: colors.background }]}>
            <View style={[styles.featureIcon, { backgroundColor: colors.secondary + '20' }]}>
              <Ionicons name="heart" size={26} color={colors.secondary} />
            </View>
            <View style={styles.featureTextContainer}>
              <Text style={[styles.featureTitle, { color: colors.text }]}>Wellness</Text>
              <Text style={[styles.featureText, { color: colors.textSecondary }]}>Track mood & habits</Text>
            </View>
          </View>
        </View>

        <View style={styles.welcomeMessage}>
          <Text style={[styles.welcomeTitle, { color: colors.text }]}>Welcome to Your Wellness Journey</Text>
          <Text style={[styles.welcomeDescription, { color: colors.textSecondary }]}>
            Livana helps you create a balanced, mindful lifestyle through personalized
            tracking, guided activities, and gentle reminders to care for yourself.
          </Text>
        </View>
      </View>

      {/* Beautiful Footer with CTA */}
      <View style={[styles.footer, { backgroundColor: colors.surface }]}>
        <TouchableOpacity
          style={[styles.getStartedButton, {
            backgroundColor: colors.secondary,
            shadowColor: colors.secondary,
          }]}
          onPress={handleGetStarted}
          activeOpacity={0.8}
        >
          <Text style={[styles.getStartedText, { color: colors.surface }]}>Get Started</Text>
          <Ionicons name="arrow-forward" size={22} color={colors.surface} />
        </TouchableOpacity>

        <Text style={[styles.footerText, { color: colors.textLight }]}>
          Let's personalize your experience with a quick lifestyle quiz
        </Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  header: {
    flex: 0.4,
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: Platform.OS === 'ios' ? spacing.safeAreaTop : spacing.xl,
    paddingHorizontal: spacing.lg,
  },

  logoContainer: {
    width: 130,
    height: 130,
    borderRadius: 65,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.lg,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },

  appName: {
    fontSize: typography.ios.largeTitle,
    fontWeight: typography.bold,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },

  tagline: {
    fontSize: typography.ios.headline,
    opacity: 0.9,
    textAlign: 'center',
    paddingHorizontal: spacing.md,
  },
  
  content: {
    flex: 0.5,
    borderTopLeftRadius: 32,
    borderTopRightRadius: 32,
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl,
  },

  featureList: {
    marginBottom: spacing.xl,
  },

  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
  },

  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },

  featureTextContainer: {
    flex: 1,
  },

  featureTitle: {
    fontSize: typography.ios.callout,
    fontWeight: typography.semibold,
    marginBottom: spacing.xs,
  },

  featureText: {
    fontSize: typography.ios.subheadline,
  },
  
  welcomeMessage: {
    alignItems: 'center',
    paddingHorizontal: spacing.md,
  },

  welcomeTitle: {
    fontSize: typography.ios.title2,
    fontWeight: typography.semibold,
    textAlign: 'center',
    marginBottom: spacing.md,
  },

  welcomeDescription: {
    fontSize: typography.ios.body,
    textAlign: 'center',
    lineHeight: 24,
  },

  footer: {
    flex: 0.1,
    paddingHorizontal: spacing.lg,
    paddingBottom: Platform.OS === 'ios' ? spacing.safeAreaBottom : spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
  },

  getStartedButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md + 2,
    paddingHorizontal: spacing.xl + 8,
    borderRadius: 28,
    marginBottom: spacing.md,
    minWidth: width * 0.7,
    minHeight: spacing.touchTarget,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 6,
  },

  getStartedText: {
    fontSize: typography.ios.headline,
    fontWeight: typography.semibold,
    marginRight: spacing.sm,
  },

  footerText: {
    fontSize: typography.ios.footnote,
    textAlign: 'center',
    opacity: 0.8,
  },
});
