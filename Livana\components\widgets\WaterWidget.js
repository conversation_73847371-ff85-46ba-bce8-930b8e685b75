import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { spacing, typography, shadows } from '../../utils/theme';
import { useTheme } from '../../context/ThemeContext';
import { saveDailyData } from '../../data/storage';

export default function WaterWidget({ data, onUpdate }) {
  const { currentTheme: colors } = useTheme();
  const glasses = data?.water || 0;
  const target = 8;
  const progress = Math.min(glasses / target, 1);

  const handleAddGlass = async () => {
    const today = new Date().toISOString().split('T')[0];
    const newGlasses = glasses + 1;
    
    try {
      await saveDailyData(today, { water: newGlasses });
      onUpdate && onUpdate();
    } catch (error) {
      console.error('Error updating water intake:', error);
    }
  };

  const handleRemoveGlass = async () => {
    if (glasses > 0) {
      const today = new Date().toISOString().split('T')[0];
      const newGlasses = glasses - 1;
      
      try {
        await saveDailyData(today, { water: newGlasses });
        onUpdate && onUpdate();
      } catch (error) {
        console.error('Error updating water intake:', error);
      }
    }
  };

  const renderWaterGlasses = () => {
    const glassElements = [];
    for (let i = 0; i < target; i++) {
      const isFilled = i < glasses;
      glassElements.push(
        <View
          key={i}
          style={[
            styles.waterGlass,
            { backgroundColor: colors.surfaceLight },
            isFilled && { backgroundColor: colors.water + '30' },
          ]}
        >
          <Ionicons
            name="water"
            size={16}
            color={isFilled ? colors.water : colors.surfaceLight}
          />
        </View>
      );
    }
    return glassElements;
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.surface, ...shadows.card }]}>
      <View style={styles.header}>
        <View style={[styles.iconContainer, { backgroundColor: colors.water + '20' }]}>
          <Ionicons name="water" size={24} color={colors.water} />
        </View>
        <Text style={[styles.title, { color: colors.text }]}>Water Intake</Text>
      </View>

      <View style={styles.content}>
        <View style={styles.valueContainer}>
          <Text style={[styles.value, { color: colors.water }]}>{glasses}</Text>
          <Text style={[styles.unit, { color: colors.textSecondary }]}>/ {target} glasses</Text>
        </View>

        <View style={styles.glassesContainer}>
          {renderWaterGlasses()}
        </View>

        <View style={styles.controls}>
          <TouchableOpacity
            style={[
              styles.controlButton,
              { backgroundColor: colors.surfaceLight },
              glasses === 0 && styles.controlButtonDisabled
            ]}
            onPress={handleRemoveGlass}
            disabled={glasses === 0}
            activeOpacity={0.7}
          >
            <Ionicons
              name="remove"
              size={20}
              color={glasses === 0 ? colors.textLight : colors.water}
            />
          </TouchableOpacity>

          <View style={styles.progressContainer}>
            <View style={[styles.progressBar, { backgroundColor: colors.surfaceLight }]}>
              <View style={[styles.progressFill, {
                width: `${progress * 100}%`,
                backgroundColor: colors.water
              }]} />
            </View>
          </View>

          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: colors.surfaceLight }]}
            onPress={handleAddGlass}
            activeOpacity={0.7}
          >
            <Ionicons name="add" size={20} color={colors.water} />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.footer}>
        <Text style={[styles.encouragement, { color: colors.textSecondary }]}>
          {glasses >= target
            ? '🎉 Great hydration today!'
            : glasses >= target * 0.5
            ? '💧 You\'re halfway there!'
            : '💦 Stay hydrated!'}
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.md,
  },

  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },

  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },

  title: {
    flex: 1,
    fontSize: typography.ios.callout,
    fontWeight: typography.semibold,
  },
  
  content: {
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: spacing.md,
  },
  
  value: {
    fontSize: typography.xxxl,
    fontWeight: typography.bold,
  },

  unit: {
    fontSize: typography.md,
    marginLeft: spacing.xs,
  },
  
  glassesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginBottom: spacing.md,
    maxWidth: 200,
  },
  
  waterGlass: {
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    margin: 2,
  },

  waterGlassFilled: {
    // backgroundColor will be set dynamically
  },
  
  controls: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  
  controlButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  
  controlButtonDisabled: {
    opacity: 0.5,
  },
  
  progressContainer: {
    flex: 1,
    marginHorizontal: spacing.md,
  },
  
  progressBar: {
    height: 6,
    borderRadius: 3,
  },

  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  
  footer: {
    alignItems: 'center',
  },
  
  encouragement: {
    fontSize: typography.sm,
    textAlign: 'center',
  },
});
