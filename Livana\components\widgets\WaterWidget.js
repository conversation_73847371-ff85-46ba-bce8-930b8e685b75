import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { spacing, typography, shadows } from '../../utils/theme';
import { useTheme } from '../../context/ThemeContext';
import { saveDailyData } from '../../data/storage';

export default function WaterWidget({ data, onUpdate }) {
  const { currentTheme: colors } = useTheme();
  const glasses = data?.water || 0;
  const target = 8;
  const progress = Math.min(glasses / target, 1);

  const handleAddGlass = async () => {
    const today = new Date().toISOString().split('T')[0];
    const newGlasses = glasses + 1;
    
    try {
      await saveDailyData(today, { water: newGlasses });
      onUpdate && onUpdate();
    } catch (error) {
      console.error('Error updating water intake:', error);
    }
  };

  const handleRemoveGlass = async () => {
    if (glasses > 0) {
      const today = new Date().toISOString().split('T')[0];
      const newGlasses = glasses - 1;
      
      try {
        await saveDailyData(today, { water: newGlasses });
        onUpdate && onUpdate();
      } catch (error) {
        console.error('Error updating water intake:', error);
      }
    }
  };

  const renderWaterGlasses = () => {
    const glassElements = [];
    for (let i = 0; i < target; i++) {
      const isFilled = i < glasses;
      glassElements.push(
        <View
          key={i}
          style={[
            styles.waterGlass,
            isFilled && styles.waterGlassFilled,
          ]}
        >
          <Ionicons
            name="water"
            size={16}
            color={isFilled ? colors.water : colors.surfaceLight}
          />
        </View>
      );
    }
    return glassElements;
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.surface, ...shadows.card }]}>
      <View style={styles.header}>
        <View style={[styles.iconContainer, { backgroundColor: colors.water + '20' }]}>
          <Ionicons name="water" size={24} color={colors.water} />
        </View>
        <Text style={[styles.title, { color: colors.text }]}>Water Intake</Text>
      </View>

      <View style={styles.content}>
        <View style={styles.valueContainer}>
          <Text style={styles.value}>{glasses}</Text>
          <Text style={styles.unit}>/ {target} glasses</Text>
        </View>

        <View style={styles.glassesContainer}>
          {renderWaterGlasses()}
        </View>

        <View style={styles.controls}>
          <TouchableOpacity
            style={[styles.controlButton, glasses === 0 && styles.controlButtonDisabled]}
            onPress={handleRemoveGlass}
            disabled={glasses === 0}
          >
            <Ionicons
              name="remove"
              size={20}
              color={glasses === 0 ? colors.textLight : colors.water}
            />
          </TouchableOpacity>

          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, { width: `${progress * 100}%` }]} />
            </View>
          </View>

          <TouchableOpacity
            style={styles.controlButton}
            onPress={handleAddGlass}
          >
            <Ionicons name="add" size={20} color={colors.water} />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.footer}>
        <Text style={styles.encouragement}>
          {glasses >= target
            ? '🎉 Great hydration today!'
            : glasses >= target * 0.5
            ? '💧 You\'re halfway there!'
            : '💦 Stay hydrated!'}
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.md,
    ...shadows.sm,
  },
  
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.water + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  
  title: {
    flex: 1,
    fontSize: typography.md,
    fontWeight: typography.semibold,
    color: colors.text,
  },
  
  content: {
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: spacing.md,
  },
  
  value: {
    fontSize: typography.xxxl,
    fontWeight: typography.bold,
    color: colors.water,
  },
  
  unit: {
    fontSize: typography.md,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
  },
  
  glassesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginBottom: spacing.md,
    maxWidth: 200,
  },
  
  waterGlass: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: colors.surfaceLight,
    alignItems: 'center',
    justifyContent: 'center',
    margin: 2,
  },
  
  waterGlassFilled: {
    backgroundColor: colors.water + '30',
  },
  
  controls: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  
  controlButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.surfaceLight,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  controlButtonDisabled: {
    opacity: 0.5,
  },
  
  progressContainer: {
    flex: 1,
    marginHorizontal: spacing.md,
  },
  
  progressBar: {
    height: 6,
    backgroundColor: colors.surfaceLight,
    borderRadius: 3,
  },
  
  progressFill: {
    height: '100%',
    backgroundColor: colors.water,
    borderRadius: 3,
  },
  
  footer: {
    alignItems: 'center',
  },
  
  encouragement: {
    fontSize: typography.sm,
    color: colors.textSecondary,
    textAlign: 'center',
  },
});
