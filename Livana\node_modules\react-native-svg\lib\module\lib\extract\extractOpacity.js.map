{"version": 3, "names": ["extractOpacity", "opacity", "value", "trim", "endsWith", "slice", "isNaN", "Math", "max"], "sourceRoot": "../../../../src", "sources": ["lib/extract/extractOpacity.ts"], "mappings": "AAEA,eAAe,SAASA,cAAcA,CAACC,OAA0B,EAAE;EACjE,MAAMC,KAAK,GACT,OAAOD,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAACE,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,GAAG,CAAC,GACvD,CAACH,OAAO,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,GAC3B,CAACJ,OAAO;EACd,OAAOK,KAAK,CAACJ,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,GAAG,CAAC,GAAGK,IAAI,CAACC,GAAG,CAACN,KAAK,EAAE,CAAC,CAAC;AAC3D", "ignoreList": []}