import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage keys
export const STORAGE_KEYS = {
  USER_PROFILE: 'user_profile',
  ONBOARDING_COMPLETE: 'onboarding_complete',
  DASHBOARD_WIDGETS: 'dashboard_widgets',
  DAILY_DATA: 'daily_data',
  HABITS: 'habits',
  WORKOUTS: 'workouts',
  MEALS: 'meals',
  JOURNAL_ENTRIES: 'journal_entries',
  GOALS: 'goals',
  SETTINGS: 'settings',
};

// Generic storage functions
export const storeData = async (key, value) => {
  try {
    const jsonValue = JSON.stringify(value);
    await AsyncStorage.setItem(key, jsonValue);
    return true;
  } catch (error) {
    console.error('Error storing data:', error);
    return false;
  }
};

export const getData = async (key, defaultValue = null) => {
  try {
    const jsonValue = await AsyncStorage.getItem(key);
    return jsonValue != null ? JSON.parse(jsonValue) : defaultValue;
  } catch (error) {
    console.error('Error retrieving data:', error);
    return defaultValue;
  }
};

export const removeData = async (key) => {
  try {
    await AsyncStorage.removeItem(key);
    return true;
  } catch (error) {
    console.error('Error removing data:', error);
    return false;
  }
};

export const clearAllData = async () => {
  try {
    await AsyncStorage.clear();
    return true;
  } catch (error) {
    console.error('Error clearing all data:', error);
    return false;
  }
};

// Specific data functions
export const saveUserProfile = async (profile) => {
  return await storeData(STORAGE_KEYS.USER_PROFILE, profile);
};

export const getUserProfile = async () => {
  return await getData(STORAGE_KEYS.USER_PROFILE, {
    name: '',
    age: null,
    weight: null,
    height: null,
    goals: [],
    visionStatement: '',
    preferences: {},
  });
};

export const setOnboardingComplete = async (isComplete = true) => {
  return await storeData(STORAGE_KEYS.ONBOARDING_COMPLETE, isComplete);
};

export const getOnboardingStatus = async () => {
  return await getData(STORAGE_KEYS.ONBOARDING_COMPLETE, false);
};

export const saveDashboardWidgets = async (widgets) => {
  return await storeData(STORAGE_KEYS.DASHBOARD_WIDGETS, widgets);
};

export const getDashboardWidgets = async () => {
  return await getData(STORAGE_KEYS.DASHBOARD_WIDGETS, [
    'steps',
    'water',
    'mood',
    'sleep',
    'habits',
    'quote',
  ]);
};

export const saveDailyData = async (date, data) => {
  const dailyData = await getData(STORAGE_KEYS.DAILY_DATA, {});
  dailyData[date] = { ...dailyData[date], ...data };
  return await storeData(STORAGE_KEYS.DAILY_DATA, dailyData);
};

export const getDailyData = async (date) => {
  const dailyData = await getData(STORAGE_KEYS.DAILY_DATA, {});
  return dailyData[date] || {
    steps: 0,
    water: 0,
    mood: null,
    sleep: 0,
    weight: null,
    habits: {},
    workouts: [],
    meals: [],
    journalEntry: '',
  };
};

export const saveHabits = async (habits) => {
  return await storeData(STORAGE_KEYS.HABITS, habits);
};

export const getHabits = async () => {
  return await getData(STORAGE_KEYS.HABITS, []);
};

export const saveWorkout = async (workout) => {
  const workouts = await getData(STORAGE_KEYS.WORKOUTS, []);
  workouts.push({
    ...workout,
    id: Date.now().toString(),
    timestamp: new Date().toISOString(),
  });
  return await storeData(STORAGE_KEYS.WORKOUTS, workouts);
};

export const getWorkouts = async () => {
  return await getData(STORAGE_KEYS.WORKOUTS, []);
};

export const saveMeal = async (meal) => {
  const meals = await getData(STORAGE_KEYS.MEALS, []);
  meals.push({
    ...meal,
    id: Date.now().toString(),
    timestamp: new Date().toISOString(),
  });
  return await storeData(STORAGE_KEYS.MEALS, meals);
};

export const getMeals = async () => {
  return await getData(STORAGE_KEYS.MEALS, []);
};

export const saveJournalEntry = async (entry) => {
  const entries = await getData(STORAGE_KEYS.JOURNAL_ENTRIES, []);
  entries.push({
    ...entry,
    id: Date.now().toString(),
    timestamp: new Date().toISOString(),
  });
  return await storeData(STORAGE_KEYS.JOURNAL_ENTRIES, entries);
};

export const getJournalEntries = async () => {
  return await getData(STORAGE_KEYS.JOURNAL_ENTRIES, []);
};

export const saveGoals = async (goals) => {
  return await storeData(STORAGE_KEYS.GOALS, goals);
};

export const getGoals = async () => {
  return await getData(STORAGE_KEYS.GOALS, []);
};

export const saveSettings = async (settings) => {
  return await storeData(STORAGE_KEYS.SETTINGS, settings);
};

export const getSettings = async () => {
  return await getData(STORAGE_KEYS.SETTINGS, {
    notifications: {
      hydration: true,
      habits: true,
      mindfulness: true,
    },
    units: {
      weight: 'kg',
      height: 'cm',
      distance: 'km',
    },
    privacy: {
      dataSharing: false,
      analytics: true,
    },
  });
};
