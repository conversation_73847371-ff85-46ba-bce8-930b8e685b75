import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography, commonStyles } from '../utils/theme';

export default function FitnessScreen({ navigation }) {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Fitness</Text>
          <TouchableOpacity style={styles.addButton}>
            <Ionicons name="add" size={24} color={colors.surface} />
          </TouchableOpacity>
        </View>

        {/* Quick Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Ionicons name="flame" size={24} color={colors.secondary} />
            <Text style={styles.statValue}>0</Text>
            <Text style={styles.statLabel}>Calories Burned</Text>
          </View>
          
          <View style={styles.statCard}>
            <Ionicons name="barbell" size={24} color={colors.primary} />
            <Text style={styles.statValue}>0</Text>
            <Text style={styles.statLabel}>Workouts This Week</Text>
          </View>
        </View>

        {/* Workout Categories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Workout Categories</Text>
          
          <View style={styles.categoriesGrid}>
            {[
              { name: 'Cardio', icon: 'heart', color: colors.error },
              { name: 'Strength', icon: 'barbell', color: colors.primary },
              { name: 'Yoga', icon: 'leaf', color: colors.mood },
              { name: 'Flexibility', icon: 'body', color: colors.secondary },
            ].map((category) => (
              <TouchableOpacity key={category.name} style={styles.categoryCard}>
                <View style={[styles.categoryIcon, { backgroundColor: category.color + '20' }]}>
                  <Ionicons name={category.icon} size={24} color={category.color} />
                </View>
                <Text style={styles.categoryName}>{category.name}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Recent Workouts */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Workouts</Text>
          <View style={styles.emptyState}>
            <Ionicons name="fitness" size={48} color={colors.textLight} />
            <Text style={styles.emptyStateText}>No workouts logged yet</Text>
            <Text style={styles.emptyStateSubtext}>Start tracking your fitness journey!</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  
  content: {
    flex: 1,
  },
  
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
  },
  
  title: {
    fontSize: typography.xxl,
    fontWeight: typography.bold,
    color: colors.text,
  },
  
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    gap: spacing.md,
  },
  
  statCard: {
    flex: 1,
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: spacing.md,
    alignItems: 'center',
    ...commonStyles.card,
  },
  
  statValue: {
    fontSize: typography.xl,
    fontWeight: typography.bold,
    color: colors.text,
    marginTop: spacing.xs,
  },
  
  statLabel: {
    fontSize: typography.sm,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: spacing.xs,
  },
  
  section: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  
  sectionTitle: {
    fontSize: typography.lg,
    fontWeight: typography.semibold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
  },
  
  categoryCard: {
    width: '47%',
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: spacing.md,
    alignItems: 'center',
    ...commonStyles.card,
  },
  
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.sm,
  },
  
  categoryName: {
    fontSize: typography.md,
    fontWeight: typography.medium,
    color: colors.text,
  },
  
  emptyState: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: spacing.xl,
    alignItems: 'center',
    ...commonStyles.card,
  },
  
  emptyStateText: {
    fontSize: typography.md,
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  
  emptyStateSubtext: {
    fontSize: typography.sm,
    color: colors.textLight,
    marginTop: spacing.xs,
  },
});
