{"version": 3, "names": ["React", "extractText", "extractProps", "propsAndStyles", "extractTransform", "pickNotNil", "<PERSON><PERSON><PERSON>", "RNSVGText", "Text", "displayName", "setNativeProps", "props", "matrix", "prop", "Object", "assign", "root", "render", "x", "y", "ref", "refMethod", "createElement"], "sourceRoot": "../../../src", "sources": ["elements/Text.tsx"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,YAAY,IAAIC,cAAc,QAAQ,6BAA6B;AAC1E,OAAOC,gBAAgB,MAAM,iCAAiC;AAO9D,SAASC,UAAU,QAAQ,aAAa;AACxC,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAO,SAAS;AAChB,OAAOC,SAAS,MAAM,+BAA+B;AAarD,eAAe,MAAMC,IAAI,SAASF,KAAK,CAAY;EACjD,OAAOG,WAAW,GAAG,MAAM;EAE3BC,cAAc,GACZC,KAGC,IACE;IACH,MAAMC,MAAM,GAAGD,KAAK,IAAI,CAACA,KAAK,CAACC,MAAM,IAAIR,gBAAgB,CAACO,KAAK,CAAC;IAChE,IAAIC,MAAM,EAAE;MACVD,KAAK,CAACC,MAAM,GAAGA,MAAM;IACvB;IACA,MAAMC,IAAI,GAAGV,cAAc,CAACQ,KAAK,CAAC;IAClCG,MAAM,CAACC,MAAM,CAACF,IAAI,EAAER,UAAU,CAACJ,WAAW,CAACY,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IACxD,IAAI,CAACG,IAAI,IAAI,IAAI,CAACA,IAAI,CAACN,cAAc,CAACG,IAAI,CAAC;EAC7C,CAAC;EAEDI,MAAMA,CAAA,EAAG;IACP,MAAMJ,IAAI,GAAGV,cAAc,CAAC,IAAI,CAACQ,KAAK,CAAC;IACvC,MAAMA,KAAK,GAAGT,YAAY,CACxB;MACE,GAAGW,IAAI;MACPK,CAAC,EAAE,IAAI;MACPC,CAAC,EAAE;IACL,CAAC,EACD,IACF,CAAC;IACDL,MAAM,CAACC,MAAM,CAACJ,KAAK,EAAEV,WAAW,CAACY,IAAI,EAAE,IAAI,CAAC,CAAC;IAC7CF,KAAK,CAACS,GAAG,GAAG,IAAI,CAACC,SAAiD;IAClE,oBAAOrB,KAAA,CAAAsB,aAAA,CAACf,SAAS,EAAKI,KAAQ,CAAC;EACjC;AACF", "ignoreList": []}