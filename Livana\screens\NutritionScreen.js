import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing, typography, commonStyles } from '../utils/theme';

export default function NutritionScreen({ navigation }) {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Nutrition</Text>
          <TouchableOpacity style={styles.addButton}>
            <Ionicons name="add" size={24} color={colors.surface} />
          </TouchableOpacity>
        </View>

        {/* Daily Summary */}
        <View style={styles.summaryContainer}>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>0</Text>
            <Text style={styles.summaryLabel}>Calories</Text>
          </View>
          
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>0</Text>
            <Text style={styles.summaryLabel}>Water (glasses)</Text>
          </View>
          
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>0</Text>
            <Text style={styles.summaryLabel}>Meals logged</Text>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          
          <View style={styles.actionsGrid}>
            {[
              { name: 'Log Meal', icon: 'restaurant', color: colors.secondary },
              { name: 'Add Water', icon: 'water', color: colors.water },
              { name: 'Recipes', icon: 'book', color: colors.primary },
              { name: 'Grocery List', icon: 'list', color: colors.mood },
            ].map((action) => (
              <TouchableOpacity key={action.name} style={styles.actionCard}>
                <View style={[styles.actionIcon, { backgroundColor: action.color + '20' }]}>
                  <Ionicons name={action.icon} size={24} color={action.color} />
                </View>
                <Text style={styles.actionName}>{action.name}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Today's Meals */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Today's Meals</Text>
          
          {['Breakfast', 'Lunch', 'Dinner', 'Snacks'].map((meal) => (
            <TouchableOpacity key={meal} style={styles.mealCard}>
              <View style={styles.mealInfo}>
                <Text style={styles.mealName}>{meal}</Text>
                <Text style={styles.mealCalories}>0 calories</Text>
              </View>
              <Ionicons name="add-circle-outline" size={24} color={colors.primary} />
            </TouchableOpacity>
          ))}
        </View>

        {/* Healthy Recipes */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Healthy Recipes</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.emptyState}>
            <Ionicons name="restaurant" size={48} color={colors.textLight} />
            <Text style={styles.emptyStateText}>Discover healthy recipes</Text>
            <Text style={styles.emptyStateSubtext}>Coming soon!</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  
  content: {
    flex: 1,
  },
  
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
  },
  
  title: {
    fontSize: typography.xxl,
    fontWeight: typography.bold,
    color: colors.text,
  },
  
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.secondary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  summaryContainer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    gap: spacing.sm,
  },
  
  summaryCard: {
    flex: 1,
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: spacing.md,
    alignItems: 'center',
    ...commonStyles.card,
  },
  
  summaryValue: {
    fontSize: typography.xl,
    fontWeight: typography.bold,
    color: colors.text,
  },
  
  summaryLabel: {
    fontSize: typography.xs,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: spacing.xs,
  },
  
  section: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  
  sectionTitle: {
    fontSize: typography.lg,
    fontWeight: typography.semibold,
    color: colors.text,
  },
  
  seeAllText: {
    fontSize: typography.sm,
    color: colors.primary,
    fontWeight: typography.medium,
  },
  
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
  },
  
  actionCard: {
    width: '47%',
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: spacing.md,
    alignItems: 'center',
    ...commonStyles.card,
  },
  
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.sm,
  },
  
  actionName: {
    fontSize: typography.sm,
    fontWeight: typography.medium,
    color: colors.text,
  },
  
  mealCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: spacing.md,
    marginBottom: spacing.sm,
    ...commonStyles.card,
  },
  
  mealInfo: {
    flex: 1,
  },
  
  mealName: {
    fontSize: typography.md,
    fontWeight: typography.medium,
    color: colors.text,
  },
  
  mealCalories: {
    fontSize: typography.sm,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  
  emptyState: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: spacing.xl,
    alignItems: 'center',
    ...commonStyles.card,
  },
  
  emptyStateText: {
    fontSize: typography.md,
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  
  emptyStateSubtext: {
    fontSize: typography.sm,
    color: colors.textLight,
    marginTop: spacing.xs,
  },
});
