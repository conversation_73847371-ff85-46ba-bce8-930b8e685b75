import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  Animated,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import { spacing, typography } from '../../utils/theme';
import { useTheme } from '../../context/ThemeContext';
import { saveUserProfile, setOnboardingComplete } from '../../data/storage';
import { useApp } from '../../context/AppContext';

export default function OnboardingCompleteScreen({ navigation, route }) {
  const { currentTheme: colors } = useTheme();
  const { userProfile } = route.params || {};
  const { completeOnboarding } = useApp();
  const fadeAnim = new Animated.Value(0);
  const scaleAnim = new Animated.Value(0.8);

  useEffect(() => {
    // Save user profile and mark onboarding as complete
    const saveData = async () => {
      try {
        await saveUserProfile(userProfile);
        await setOnboardingComplete(true);
      } catch (error) {
        console.error('Error saving user data:', error);
      }
    };

    saveData();

    // Animate entrance
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleStartJourney = () => {
    // Complete onboarding which will trigger navigation to main app
    completeOnboarding();
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar style="light" backgroundColor={colors.primary} />
      
      <Animated.View 
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        {/* Success Icon */}
        <View style={styles.iconContainer}>
          <View style={styles.successIcon}>
            <Ionicons name="checkmark-circle" size={80} color={colors.success} />
          </View>
          <View style={styles.sparkles}>
            <Ionicons name="sparkles" size={24} color={colors.secondary} style={styles.sparkle1} />
            <Ionicons name="sparkles" size={20} color={colors.secondary} style={styles.sparkle2} />
            <Ionicons name="sparkles" size={16} color={colors.secondary} style={styles.sparkle3} />
          </View>
        </View>

        {/* Welcome Message */}
        <View style={styles.messageContainer}>
          <Text style={[styles.welcomeText, { color: colors.primary }]}>Welcome to Livana!</Text>
          <Text style={[styles.nameText, { color: colors.text }]}>
            {userProfile?.name ? `Hello, ${userProfile.name}!` : 'Hello there!'}
          </Text>
          <Text style={[styles.descriptionText, { color: colors.textSecondary }]}>
            Your personalized wellness journey is ready to begin. We've customized
            your experience based on your goals and preferences.
          </Text>
        </View>

        {/* Summary Cards */}
        <View style={styles.summaryContainer}>
          <View style={[styles.summaryCard, { backgroundColor: colors.surface }]}>
            <Ionicons name="target" size={24} color={colors.primary} />
            <Text style={[styles.summaryTitle, { color: colors.textSecondary }]}>Goals Set</Text>
            <Text style={[styles.summaryValue, { color: colors.text }]}>
              {userProfile?.selectedGoals?.length || 0} wellness goals
            </Text>
          </View>

          <View style={[styles.summaryCard, { backgroundColor: colors.surface }]}>
            <Ionicons name="person" size={24} color={colors.secondary} />
            <Text style={[styles.summaryTitle, { color: colors.textSecondary }]}>Profile</Text>
            <Text style={[styles.summaryValue, { color: colors.text }]}>
              Personalized for you
            </Text>
          </View>
        </View>

        {/* Vision Statement Preview */}
        {userProfile?.visionStatement && (
          <View style={[styles.visionContainer, { backgroundColor: colors.primaryLight + '20' }]}>
            <Text style={[styles.visionTitle, { color: colors.primary }]}>Your Wellness Vision</Text>
            <Text style={[styles.visionText, { color: colors.text }]}>"{userProfile.visionStatement}"</Text>
          </View>
        )}

        {/* Features Preview */}
        <View style={[styles.featuresContainer, { backgroundColor: colors.surface }]}>
          <Text style={[styles.featuresTitle, { color: colors.text }]}>What's waiting for you:</Text>
          <View style={styles.featuresList}>
            <View style={styles.featureItem}>
              <Ionicons name="home" size={16} color={colors.primary} />
              <Text style={[styles.featureText, { color: colors.textSecondary }]}>Personalized dashboard</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="notifications" size={16} color={colors.primary} />
              <Text style={[styles.featureText, { color: colors.textSecondary }]}>Gentle wellness reminders</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="analytics" size={16} color={colors.primary} />
              <Text style={[styles.featureText, { color: colors.textSecondary }]}>Progress tracking & insights</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="heart" size={16} color={colors.primary} />
              <Text style={[styles.featureText, { color: colors.textSecondary }]}>Daily check-ins & coaching</Text>
            </View>
          </View>
        </View>
      </Animated.View>

      {/* Start Journey Button */}
      <View style={styles.footer}>
        <TouchableOpacity style={[styles.startButton, { backgroundColor: colors.primary }]} onPress={handleStartJourney}>
          <Text style={[styles.startButtonText, { color: colors.surface }]}>Start My Wellness Journey</Text>
          <Ionicons name="arrow-forward" size={20} color={colors.surface} />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl,
    alignItems: 'center',
  },
  
  iconContainer: {
    position: 'relative',
    marginBottom: spacing.xl,
  },
  
  successIcon: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  sparkles: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  
  sparkle1: {
    position: 'absolute',
    top: -10,
    right: -10,
  },
  
  sparkle2: {
    position: 'absolute',
    bottom: 10,
    left: -15,
  },
  
  sparkle3: {
    position: 'absolute',
    top: 20,
    left: -20,
  },
  
  messageContainer: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  
  welcomeText: {
    fontSize: typography.xxxl,
    fontWeight: typography.bold,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },

  nameText: {
    fontSize: typography.xl,
    fontWeight: typography.medium,
    textAlign: 'center',
    marginBottom: spacing.md,
  },

  descriptionText: {
    fontSize: typography.md,
    textAlign: 'center',
    lineHeight: 22,
    paddingHorizontal: spacing.md,
  },
  
  summaryContainer: {
    flexDirection: 'row',
    marginBottom: spacing.lg,
  },
  
  summaryCard: {
    flex: 1,
    borderRadius: 12,
    padding: spacing.md,
    alignItems: 'center',
    marginHorizontal: spacing.xs,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  summaryTitle: {
    fontSize: typography.sm,
    fontWeight: typography.medium,
    marginTop: spacing.xs,
    marginBottom: spacing.xs,
  },

  summaryValue: {
    fontSize: typography.sm,
    textAlign: 'center',
  },
  
  visionContainer: {
    borderRadius: 12,
    padding: spacing.lg,
    marginBottom: spacing.lg,
    width: '100%',
  },

  visionTitle: {
    fontSize: typography.md,
    fontWeight: typography.semibold,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },

  visionText: {
    fontSize: typography.sm,
    fontStyle: 'italic',
    textAlign: 'center',
    lineHeight: 20,
  },

  featuresContainer: {
    width: '100%',
    borderRadius: 12,
    padding: spacing.lg,
  },

  featuresTitle: {
    fontSize: typography.md,
    fontWeight: typography.semibold,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  
  featuresList: {
    alignItems: 'flex-start',
  },
  
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  
  featureText: {
    fontSize: typography.sm,
    marginLeft: spacing.sm,
  },

  footer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
    alignItems: 'center',
  },

  startButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.xl,
    borderRadius: 25,
    minWidth: 250,
    justifyContent: 'center',
  },

  startButtonText: {
    fontSize: typography.lg,
    fontWeight: typography.semibold,
    marginRight: spacing.sm,
  },
});
