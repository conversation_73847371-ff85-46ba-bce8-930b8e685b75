import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

// Import onboarding screens
import WelcomeScreen from '../screens/onboarding/WelcomeScreen';
import LifestyleQuizScreen from '../screens/onboarding/LifestyleQuizScreen';
import GoalSettingScreen from '../screens/onboarding/GoalSettingScreen';
import PersonalizationScreen from '../screens/onboarding/PersonalizationScreen';
import OnboardingCompleteScreen from '../screens/onboarding/OnboardingCompleteScreen';

const Stack = createStackNavigator();

export default function OnboardingNavigator() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#8FBC8F', // Sage green
        },
        headerTintColor: '#FFFFFF',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen 
        name="Welcome" 
        component={WelcomeScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen 
        name="LifestyleQuiz" 
        component={LifestyleQuizScreen}
        options={{ title: 'Lifestyle Quiz' }}
      />
      <Stack.Screen 
        name="GoalSetting" 
        component={GoalSettingScreen}
        options={{ title: 'Set Your Goals' }}
      />
      <Stack.Screen 
        name="Personalization" 
        component={PersonalizationScreen}
        options={{ title: 'Personalize' }}
      />
      <Stack.Screen 
        name="OnboardingComplete" 
        component={OnboardingCompleteScreen}
        options={{ headerShown: false }}
      />
    </Stack.Navigator>
  );
}
