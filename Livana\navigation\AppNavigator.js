import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';

// Import screens
import OnboardingNavigator from './OnboardingNavigator';
import DashboardScreen from '../screens/DashboardScreen';
import FitnessScreen from '../screens/FitnessScreen';
import NutritionScreen from '../screens/NutritionScreen';
import MindfulnessScreen from '../screens/MindfulnessScreen';
import AgendaScreen from '../screens/AgendaScreen';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Main tab navigator for authenticated users
function MainTabNavigator() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Dashboard') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Fitness') {
            iconName = focused ? 'fitness' : 'fitness-outline';
          } else if (route.name === 'Nutrition') {
            iconName = focused ? 'restaurant' : 'restaurant-outline';
          } else if (route.name === 'Mindfulness') {
            iconName = focused ? 'leaf' : 'leaf-outline';
          } else if (route.name === 'Agenda') {
            iconName = focused ? 'calendar' : 'calendar-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#8FBC8F', // Sage green
        tabBarInactiveTintColor: '#A0A0A0',
        tabBarStyle: {
          backgroundColor: '#FFFFFF',
          borderTopColor: '#E0E0E0',
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        headerStyle: {
          backgroundColor: '#8FBC8F',
        },
        headerTintColor: '#FFFFFF',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      <Tab.Screen 
        name="Dashboard" 
        component={DashboardScreen}
        options={{ title: 'Home' }}
      />
      <Tab.Screen 
        name="Fitness" 
        component={FitnessScreen}
        options={{ title: 'Fitness' }}
      />
      <Tab.Screen 
        name="Nutrition" 
        component={NutritionScreen}
        options={{ title: 'Nutrition' }}
      />
      <Tab.Screen 
        name="Mindfulness" 
        component={MindfulnessScreen}
        options={{ title: 'Mindfulness' }}
      />
      <Tab.Screen 
        name="Agenda" 
        component={AgendaScreen}
        options={{ title: 'Agenda' }}
      />
    </Tab.Navigator>
  );
}

// Main app navigator
export default function AppNavigator() {
  // TODO: Add logic to check if user has completed onboarding
  const hasCompletedOnboarding = false; // This will be managed by app state

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {!hasCompletedOnboarding ? (
          <Stack.Screen name="Onboarding" component={OnboardingNavigator} />
        ) : (
          <Stack.Screen name="Main" component={MainTabNavigator} />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
}
